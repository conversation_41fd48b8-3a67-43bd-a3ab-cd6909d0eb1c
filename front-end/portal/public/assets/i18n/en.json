{"general": {"search": "Search...", "select": "Select", "buttons": {"save": "Save", "confirm": "Confirm", "cancel": "Cancel", "delete": "Delete", "create": "Create", "update": "Update", "complete": "Complete", "review": "Review", "reset": "Reset", "add": "Add", "overview": "Overview", "accept": "Accept", "reject": "Reject", "edit": "Edit", "done": "Done", "preview": "Preview", "share": "Share", "back_to_portal": "Back to portal", "next": "Next", "previous": "Previous", "mark_complete": "<PERSON><PERSON>", "close": "Close", "change_selection": "Change selection", "mark_as_complete": "Mark as complete", "remove": "Remove", "retry": "Retry", "refresh": "Refresh", "send": "Send", "login": "<PERSON><PERSON>", "register": "Register", "select_all": "Select all", "deselect_all": "Deselect all", "open": "Open"}, "selected_all": "All selected", "unknown": "Unknown", "back": "Back", "toasts": {"error": {"title": "Oops, something went wrong!", "description": "It looks like something went wrong on our end. Please try again or contact our support."}, "success": {"title": "Success!", "description": "The action you performed has been successfully completed."}}, "bool": {"true": "Yes", "false": "No"}, "manage": "Manage", "empty_data": "It looks like there is no data available.", "from": "From", "until": "Until", "to": "To", "filter": "Filter", "sort": "Sort by", "settings": "Settings", "request_support": "Request support", "date": "Date", "google": "Google", "microsoft": "Microsoft", "facebook": "Facebook", "linkedin": "LinkedIn", "pinterest": "Pinterest", "tiktok": "TikTok", "instagram": "Instagram", "connected": "Connected", "error": "Error", "preview_mode": "Preview mode. Open in incognito to exit preview mode.", "skipped": "Skipped", "skip": "<PERSON><PERSON>", "synced": "Synced", "connect": "Connect", "failed": "Failed", "failed_data": "Successful", "success": "Success", "pending": "Pending", "connect_your_accounts": "Connect your accounts", "add_another": "Add another one", "skipped_drawer": {"title": "Connection skipped!", "description": "It looks like you have skipped this connection."}, "syncing": "Syncing", "empty_placeholder": "There are no entry's found"}, "pages": {"auth": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "email_required": "Email is required", "invalid_email": "Please enter a valid email address", "password": "Password", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "no_account": "Don't have an account?", "register": "Register now"}, "register": {"title": "Register", "name": "Company Name", "name_required": "Company name is required", "firstname": "First Name", "firstname_required": "First name is required", "lastname": "Last Name", "lastname_required": "Last name is required", "email": "Email", "email_required": "Email is required", "invalid_email": "Please enter a valid email address", "password": "Password", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "already_account": "Already have an account?", "login": "Login now"}}, "settings": {"sidebar": {"general": "General", "users": "Users", "google": "Google", "integrations": "Integrations", "add-ons": "Add-ons", "emails": "Emails", "billing": "Billing"}, "users": {"index": {"table": {"name": "Name", "email": "Email", "role": "Role"}, "add": "Add user"}, "detail": {"inputs": {"role": "Role", "firstname": "Firstname", "lastname": "Lastname", "image": "Image", "email": "Email"}}}, "google": {"table": {"name": "Name", "email": "Email"}, "delete": {"title": "Delete Google account?", "description": "Are you sure you want to delete this Google account?"}}, "add-ons": {"quality-score": {"title": "Quality Score Checker", "description": "Monitor and track your ad quality scores", "buttons": {"start": "Start setup guide", "collapse": "Collapse", "add": "Add to dashboard"}, "connected-accounts": "Connected Google Ad Accounts", "account-id": "Account ID:", "steps": {"SELECT_ACCOUNT": {"title": "Select Google Ad Account", "description": "Choose the Google Ad account you want to enable the quality score monitoring for."}, "SELECT_DRIVE": {"title": "Select Google Drive", "description": "Select or create an Google Drive to store the sheet in.", "inputs": {"name": "Or create Google Shared Drive by entering a name below"}}, "CREATING_SHEET": {"title": "Creating Google Sheet", "description": "Please wait so we can create the Google Sheet.", "progress": "Creating your quality score sheet...", "errors": {"UNKNOWN": "Something unexpected happened. Please contact support."}}, "ADD_SCRIPT": {"title": "Add script to Google Ad Account", "description": "Add the generated script to your Google Ad account to start monitoring.", "loading": "Loading your quality score script...", "steps": {"0": {"title": "Navigate to scripts dashboard.", "description": "Click on the link below", "click": "Click here"}, "1": {"title": "Select Ad account", "description": "Select the Ad account on which you want to add this script."}, "2": {"title": "Click the plus", "description": "Click the blue plus icon to add a new script and select new script."}, "3": {"title": "Copy and paste", "description": "Copy and paste the script below and click on save then click on close."}, "4": {"title": "Set frequency", "description": "Now look in the table for the script you just created. Next navigate to the frequency column and click on the pen icon. Set the frequency to daily and choose an time before 3 am."}, "5": {"title": "Finished", "description": "Now that you have completed all steps we automatically will sync the data everyday starting tomorrow. You can now close the guide."}}, "toasts": {"copy": {"title": "<PERSON><PERSON><PERSON> copied!", "description": "<PERSON><PERSON><PERSON> successfully copied to clipboard."}}}}}, "auction-insights": {"title": "Auction Insights", "description": "Deep dive into auction data to understand your competitive landscape and optimize bidding strategies.", "buttons": {"start": "Start setup guide", "collapse": "Collapse", "add": "Add to dashboard"}, "connected-accounts": "Connected Google Ad Accounts", "account-id": "Account ID:", "steps": {"SELECT_ACCOUNT": {"title": "Select Google Ad Account", "description": "Choose the Google Ad account you want to enable the quality score monitoring for."}, "CREATE_EXPORT": {"title": "Create export in Google Ads", "description": "Follow the step by step guide below to create an export to an Google Sheet.", "steps": {"0": {"title": "Navigate to Auction Insights dashboard.", "description": "Click on the link below", "click": "Click here"}, "1": {"title": "Select Ad account", "description": "Select the Ad account on which you want to add this script."}, "2": {"title": "Click on download", "description": "Click on download and select schedule"}, "3": {"title": "Set interval", "description": "To ensure an daily export please select daily and a time before 3 am."}, "4": {"title": "Select export type", "description": "By default it says excel. You want to click on this and select Google Spreadsheets"}, "5": {"title": "Set receiver", "description": "In the receiver area you need to put your own email."}, "6": {"title": "File name and drive", "description": "For file name you can choose anything that you want. To ensure we get an good export please select an empty shared drive which is only for these exports."}, "7": {"title": "Set segments", "description": "Please add the following segment: Day"}, "8": {"title": "Set includes", "description": "For the includes please uncheck the following boxes: Title and date range, Totals"}, "9": {"title": "Done", "description": "You are now done and can click on schedule. After you have clicked on schedule please click on next to go to the next step."}}}, "SELECT_DRIVE": {"title": "Select Google Drive", "description": "Select the Google Drive you have just selected in the automation in Google Ads."}, "FINISHED": {"title": "Finished", "description": "You have now successfully created the Auction Insights export. Please click on 'Add to Dashboard' to complete the guide and add it in our system."}}}}, "emails": {"index": {"title": "Emails", "select_language": "Select language...", "table": {"name": "Name", "updated_at": "Updated at", "updated_by": "Updated by", "language": "Language", "add-language": "Language", "send-preview": "Send preview"}, "add-language": {"title": "Add new email language", "language": "Language"}, "language": "Language", "support": "Need help with your e-mails? Read our <a href='https://www.linkmyagency.com/docs/setting-up-your-share-link-template/' target='_blank' class='blue'>Support article</a> on this.", "preview": {"title": "Send preview mail", "description": "Are you sure you want to send a preview mail to yourself? Keep in mind the data in the email you will receive is dummy data."}, "delete": {"title": "Delete language for email", "description": "Are you sure you want to delete this language (<<language>>)?"}}, "detail": {"variables": "Variable legend", "table": {"name": "Name", "how_to_use": "How to use"}, "tabs": {"general": "General", "template": "Template"}, "inputs": {"cc_sender": "Add sender to cc", "cc": "Cc", "subject": "Subject"}, "subject_variable_info": "You can use all the variables that are defined on the template page in the subject. Simply copy and paste."}}, "billing": {"items": {"products": "Products", "information": "Information", "invoices": "Invoices"}}}, "dashboards": {"index": {"table": {"name": "Name", "created_at": "Created at", "last_change_at": "Last change at"}, "add": "Add dashboard", "delete": {"title": "Delete dashboard?", "description": "Are you sure you want to delete this dashboard?"}, "share": {"title": "Share"}}, "detail": {"steps": {"GENERAL": "General", "PAGES": "Pages", "SOURCES": "Data sources", "RECIPIENT": "Recipient", "KPIS": "KPIs"}, "general": {"title": "Dashboard configuration", "description": "Set up your dashboard template and display options.", "fields": {"name": {"title": "Dashboard title", "description": "This will be displayed at the top of your dashboard."}, "user": {"title": "Assigned employee", "description": "Employee responsible for managing this dashboard."}, "business_type": {"title": "Business type"}}}, "sources": {"title": "Data Sources", "description": "Configure your Google Ads accounts and other data sources", "google_ad_accounts": {"field": "Google ad accounts", "button": "Add Google Ad account"}}, "pages": {"pages": {"EXECUTIVE": "Executive", "TRENDS": "Trends", "METRIC_TREE": "Metric tree", "INSIGHTS": "Insights"}, "sections": {"EXECUTIVE_SUMMARY": "Executive summary", "EXECUTIVE_OVERVIEW": "Executive overview", "PERFORMANCE_TRENDS": "Performance trends", "CAMPAIGN_OVERVIEW": "Campaign overview", "PERIOD_ANALYSIS": "Period analysis", "METRIC_INFLUENCE": "Metric influence", "KEYWORD_QUALITY_SCORE": "Keyword Quality Score", "QUALITY_SCORE": "Quality Score", "COMPETITION": "Auction Insights"}}, "recipient": {"title": "Client information", "description": "Configure client details and branding for the dashboard", "fields": {"company": "Client name"}}, "kpis": {"types": {"MONTHLY_GROW_TARGET": "Monthly grow target", "EFFICIENCY_TARGET": "Efficiency target", "TOTAL_CONVERSION_VALUE": "Total conversion value", "ROAS": "ROAS"}, "target": "Target", "unit": "Unit", "year": "Year", "units": {"percentage": "Percentage", "amount": "Amount"}, "add": "Add kpi"}}}}, "layouts": {"container": {"settings": "Settings", "home": "Home", "dashboards": "Dashboards"}}, "components": {"pagination": {"showing": "Showing", "to": "to", "results": "results", "previous": "Previous", "of": "of", "next": "Next"}}, "enums": {"user-role": {"ADMIN": "Admin", "USER": "User"}, "business-type": {"LEAD_GENERATION": "Lead Generation", "SAAS": "SaaS", "E_COMMERCE": "E-commerce"}}}