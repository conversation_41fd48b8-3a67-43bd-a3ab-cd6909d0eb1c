<nav
  *transloco="let t; read: 'layouts.container'"
  class="w-full bg-white border-b border-gray-200 px-4 py-4"
>
  <div class="max-w-7xl mx-auto flex items-center justify-between">
    <div class="nav flex items-center gap-4">
      <img
        src="/assets/images/logo.png"
        alt="Logo"
        class="h-8 w-auto"
      />
      <ul class="flex items-center gap-2">
        <li>
          <a
            routerLink="dashboards"
            routerLinkActive="!bg-eaglo-black !text-white"
            class="hover:bg-eaglo-black/10 px-3 py-1 rounded flex items-center space-x-2"
          >
            <i class="fa-regular fa-chart-mixed"></i>
            <span>{{ t("dashboards") }}</span>
          </a>
        </li>
        <li>
          <a
            routerLink="settings"
            routerLinkActive="!bg-eaglo-black !text-white"
            class="hover:bg-eaglo-black/10 px-3 py-1 rounded flex items-center space-x-2"
          >
            <i class="fa-regular fa-gear"></i>
            <span>{{ t("settings") }}</span>
          </a>
        </li>
      </ul>
    </div>
    <div class="relative">
      <button
        type="button"
        (click)="toggleDropdown()"
        class="flex items-center focus:outline-none cursor-pointer"
      >
<!--        <img-->
<!--          src="/assets/user.svg"-->
<!--          alt="User"-->
<!--          class="h-8 w-8 rounded-full border border-gray-300"-->
<!--          ngOptimizedImage-->
<!--        />-->
        <i class="fa-regular fa-user"></i>
      </button>
      @if (isDropdownOpen()) {
        <div
          #container
          class="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded shadow-lg z-50"
        >
          <div class="border-t border-gray-100"></div>
          <button
            (click)="logout()"
            class="block px-4 py-2 text-eaglo-gray hover:bg-eaglo-gray-100 w-full"
          >
            Logout
          </button>
        </div>
      }
    </div>
  </div>
</nav>

<main class="max-w-7xl mx-auto py-6">
  <router-outlet></router-outlet>
</main>
