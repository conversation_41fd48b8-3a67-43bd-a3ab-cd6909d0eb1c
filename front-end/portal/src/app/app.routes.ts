import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';
import { authenticatedUserResolver } from './resolvers/authenticatedUserResolver';
import { ContainerComponent } from './layouts/container/container.component';

export const routes: Routes = [
  {
    path: '',
    canActivate: [authGuard],
    resolve: {
      me: authenticatedUserResolver,
    },
    component: ContainerComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'dashboards',
      },
      {
        path: 'settings',
        loadChildren: () =>
          import('./pages/settings/settings.routes').then((r) => r.routes),
      },
      {
        path: 'dashboards',
        loadChildren: () =>
          import('./pages/dashboard/dashboard.routes').then((r) => r.routes),
      },
    ],
  },
  {
    path: 'auth',
    loadChildren: () =>
      import('./pages/auth/auth.routes').then((m) => m.AUTH_ROUTES),
  },
];
