import {
  ApplicationConfig,
  provideZoneChangeDetection,
  EnvironmentProviders,
  makeEnvironmentProviders,
  inject,
  provideAppInitializer,
  isDevMode,
  importProvidersFrom,
} from '@angular/core';
import {
  provideRouter,
  withComponentInputBinding,
  withHashLocation,
} from '@angular/router';
import { ConfigService } from './services/config.service';

import { routes } from './app.routes';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { TranslocoHttpLoader } from './transloco-loader';
import { provideTransloco } from '@jsverse/transloco';
import { unauthenticatedInterceptor } from './interceptors/unauthenticated.interceptor';
import { provideHotToastConfig } from '@ngxpert/hot-toast';
import { en_US, provideNzI18n } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import en from '@angular/common/locales/en';
import { FormsModule } from '@angular/forms';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';

registerLocaleData(en);

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withHashLocation(), withComponentInputBinding()),
    provideAppInitializer(() => {
      const initializerFn = (
        (configService: ConfigService) => () =>
          configService.initialize().then(async ({}) => {})
      )(inject(ConfigService));
      return initializerFn();
    }),
    provideHttpClient(withInterceptors([unauthenticatedInterceptor])),
    provideTransloco({
      config: {
        availableLangs: ['en'],
        defaultLang: 'en',
        // Remove this option if your application doesn't support changing language in runtime.
        reRenderOnLangChange: true,
        prodMode: !isDevMode(),
        interpolation: ['<<', '>>'],
      },
      loader: TranslocoHttpLoader,
    }),
    provideHotToastConfig({
      autoClose: true,
      className: 'toast-container',
      position: 'top-right',
      stacking: 'depth',
      dismissible: true,
    }),
    provideNzI18n(en_US),
    importProvidersFrom(FormsModule),
    provideAnimationsAsync(),
    provideHttpClient(),
  ],
};
