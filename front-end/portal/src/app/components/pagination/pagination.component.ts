import {
  ChangeDetectionStrategy,
  Component,
  Input,
  Output,
} from '@angular/core';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { TranslocoDirective } from '@jsverse/transloco';
import { Subject } from 'rxjs';

@Component({
  selector: 'components-pagination',
  imports: [TranslocoDirective],
  templateUrl: './pagination.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaginationComponent {
  @Input({ required: true }) response!: PaginatedResponse<any> | null;
  @Output() pageChange$: Subject<number> = new Subject();

  public pageChange(page: number): void {
    this.pageChange$.next(page);
  }
}
