import { ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from '@services/auth.service';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Me } from '@api/auth/models/me.interface';

export const authenticatedUserResolver: ResolveFn<
  Observable<DataResponse<Me>>
> = (route, state) => {
  const authenticationService = inject(AuthService);
  return authenticationService.loadUser();
};
