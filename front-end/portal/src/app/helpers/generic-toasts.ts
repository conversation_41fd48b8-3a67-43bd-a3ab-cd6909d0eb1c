import { TranslocoService } from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';

export const genericToastError = (
  toastService: HotToastService,
  translocoService: TranslocoService,
  title: string | null = null,
  description: string | null = null,
) => {
  toastService.error(
    '<i class="fa-regular fa-circle-xmark text-red-500 text-2xl"></i>' +
      '<div class="content-container">' +
      '<p class="font-medium">' +
      translocoService.translate(title ?? 'general.toasts.error.title') +
      '</p>' +
      '<p>' +
      translocoService.translate(
        description ?? 'general.toasts.error.description',
      ) +
      '</p>' +
      '</div>' +
      '<div class="absolute top-2 right-4"><i class="fa-regular fa-xmark text-xl"></i></div>',
    {
      icon: '',
    },
  );
};

export const genericToastSuccess = (
  toastService: HotToastService,
  translocoService: TranslocoService,
  title: string | null = null,
  description: string | null = null,
) => {
  toastService.success(
    '<i class="fa-regular fa-circle-check text-green-500 text-2xl"></i>' +
      '<div class="content-container">' +
      '<p class="font-medium">' +
      translocoService.translate(title ?? 'general.toasts.success.title') +
      '</p>' +
      '<p>' +
      translocoService.translate(
        description ?? 'general.toasts.success.description',
      ) +
      '</p>' +
      '</div>' +
      '<div class="absolute top-2 right-4"><i class="fa-regular fa-xmark text-xl"></i></div>',
    {
      icon: '',
    },
  );
};
