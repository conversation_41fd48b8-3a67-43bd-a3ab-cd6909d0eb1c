export const bodyToFormData = (data: object): FormData => {
  const form = new FormData();

  appendToFormData(form, data);

  return form;
};

const appendToFormData = (
  form: FormData,
  data: any,
  parentKey: string = '',
): void => {
  if (checkIfIsObject(data)) {
    Object.keys(data).forEach((key) => {
      const item = data[key as keyof object];

      if (checkIfIsObject(item)) {
        appendToFormData(form, true, parentKey ? `${parentKey}[${key}]` : key);
        appendToFormData(form, item, parentKey ? `${parentKey}[${key}]` : key);
        return;
      }

      appendToFormData(form, item, parentKey ? `${parentKey}[${key}]` : key);
    });
    return;
  }

  if (typeof data === 'boolean') {
    data = data ? 1 : 0;
  }

  form.append(parentKey, data ?? '');
};

const checkIfIsObject = (data: any): boolean => {
  return (
    data &&
    typeof data === 'object' &&
    !(data instanceof File) &&
    !(data instanceof Date)
  );
};
