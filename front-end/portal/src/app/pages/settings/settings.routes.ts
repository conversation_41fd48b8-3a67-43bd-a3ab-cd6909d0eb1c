import { Routes } from '@angular/router';
import { SettingsComponent } from './settings.component';
import { BillingComponent } from '@pages/settings/pages/billing/billing.component';

export const routes: Routes = [
  {
    path: '',
    component: SettingsComponent,
    children: [
      // {
      //   path: '',
      //   pathMatch: 'full',
      //   loadComponent: () =>
      //     import('./pages/general/general.component').then(
      //       (c) => c.GeneralComponent,
      //     ),
      // },
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'users',
      },
      {
        path: 'users',
        loadChildren: () =>
          import('./pages/users/users.routes').then((r) => r.routes),
      },
      {
        path: 'google',
        loadComponent: () =>
          import('./pages/google/google.component').then(
            (c) => c.GoogleComponent,
          ),
      },
      {
        path: 'add-ons',
        loadComponent: () =>
          import('./pages/add-ons/add-ons.component').then(
            (c) => c.AddOnsComponent,
          ),
      },
      {
        path: 'emails',
        loadChildren: () =>
          import('./pages/emails/emails.routes').then((r) => r.routes),
      },
      {
        path: 'billing',
        component: BillingComponent,
        loadChildren: () =>
          import('./pages/billing/billing.routes').then((r) => r.routes),
      },
    ],
  },
];
