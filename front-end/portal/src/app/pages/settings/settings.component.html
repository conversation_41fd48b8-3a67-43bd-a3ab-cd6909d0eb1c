<div *transloco="let t; read:'pages.settings'" class="flex space-x-4 w-full">
  <aside class="w-52 h-full">
    <nav class="flex flex-col h-full">
      <ul class="space-y-2">
<!--        <li>-->
<!--          <a [routerLinkActiveOptions]="{exact: true}" routerLink="/settings" routerLinkActive="bg-white !text-eaglo-blue" class="flex items-center px-4 py-2 text-eaglo-gray hover:bg-eaglo-gray-100 hover:text-eaglo-blue group transition-colors hover:bg-white rounded">-->
<!--            <i class="fa-regular fa-circle-info w-8"></i>-->
<!--            <span>{{ t('sidebar.general') }}</span>-->
<!--          </a>-->
<!--        </li>-->
        <li>
          <a routerLink="users" routerLinkActive="bg-white !text-eaglo-blue" class="flex items-center px-4 py-2 text-eaglo-gray hover:bg-eaglo-gray-100 hover:text-eaglo-blue group transition-colors hover:bg-white rounded">
            <i class="fa-regular fa-users w-8"></i>
            <span>{{ t('sidebar.users') }}</span>
          </a>
        </li>
        <li>
          <a routerLink="google" routerLinkActive="bg-white !text-eaglo-blue" class="flex items-center px-4 py-2 text-eaglo-gray hover:bg-eaglo-gray-100 hover:text-eaglo-blue group transition-colors hover:bg-white rounded">
            <i class="fa-brands fa-google w-8"></i>
            <span>{{ t('sidebar.google') }}</span>
          </a>
        </li>
        <li>
          <a routerLink="add-ons" routerLinkActive="bg-white !text-eaglo-blue" class="flex items-center px-4 py-2 text-eaglo-gray hover:bg-eaglo-gray-100 hover:text-eaglo-blue group transition-colors hover:bg-white rounded">
            <i class="fa-regular fa-puzzle-piece-simple w-8"></i>
            <span>{{ t('sidebar.add-ons') }}</span>
          </a>
        </li>
        <li>
          <a routerLink="emails" routerLinkActive="bg-white !text-eaglo-blue" class="flex items-center px-4 py-2 text-eaglo-gray hover:bg-eaglo-gray-100 hover:text-eaglo-blue group transition-colors hover:bg-white rounded">
            <i class="fa-regular fa-envelope-open-text w-8"></i>
            <span>{{ t('sidebar.emails') }}</span>
          </a>
        </li>
        <li>
          <a routerLink="billing" routerLinkActive="bg-white !text-eaglo-blue" class="flex items-center px-4 py-2 text-eaglo-gray hover:bg-eaglo-gray-100 hover:text-eaglo-blue group transition-colors hover:bg-white rounded">
            <i class="fa-regular fa-money-bill-wave w-8"></i>
            <span>{{ t('sidebar.billing') }}</span>
          </a>
        </li>
        <li>
          <a routerLinkActive="bg-white !text-eaglo-blue" class="flex items-center px-4 py-2 text-eaglo-gray hover:bg-eaglo-gray-100 hover:text-eaglo-blue group transition-colors hover:bg-white rounded">
            <i class="fa-regular fa-wand-magic-sparkles w-8"></i>
            <span>{{ t('sidebar.integrations') }}</span>
          </a>
        </li>
      </ul>
    </nav>
  </aside>
  <div class="w-full">
    <router-outlet></router-outlet>
  </div>
</div>
