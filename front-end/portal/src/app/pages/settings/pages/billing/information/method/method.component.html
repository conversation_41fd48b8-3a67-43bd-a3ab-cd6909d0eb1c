<div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 w-screen sm:max-w-md sm:p-6">
  @if(isLoading() || submitting()) {
    <div class="flex items-center justify-center py-4">
      <i class="fa-duotone fa-spinner-third animate-spin text-5xl"></i>
    </div>
  }
  <div [class.hidden]="submitting()">
    <div id="payment-element">
      <!-- Elements will create form elements here -->
    </div>
    <div id="error-message">
      <!-- Display error message to your customers here -->
    </div>
  </div>

  @if(!isLoading()) {
    <div class="pt-4 flex justify-end">
      <button (click)="savePaymentMethod()" type="button" class="inline-block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">{{ 'general.buttons.save' | transloco }}</button>
    </div>
  }
</div>
