import { inject, Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { MethodComponent } from '@pages/settings/pages/billing/information/method/method.component';
import { take, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class MethodService {
  private modalService = inject(ModalService);

  public show(): void {
    const modal = this.modalService.attach(MethodComponent);

    modal.componentRef.instance.close$
      .pipe(
        tap(() => modal.overlayRef.detach()),
        take(1),
      )
      .subscribe();
  }
}
