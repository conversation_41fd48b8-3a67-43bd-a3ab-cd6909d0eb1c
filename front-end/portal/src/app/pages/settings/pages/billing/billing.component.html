<div *transloco="let t; read: 'pages.settings.billing'" class="space-y-4">
  <div class="border-b border-gray-200">
    <div>
      <nav class="-mb-px flex space-x-8" aria-label="Tabs">
        <a
          routerLink="information"
          routerLinkActive="!border-eaglo-blue !text-gray-900"
          class="whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 space-x-2 cursor-pointer"
        >
          <i class="fa-regular fa-circle-info"></i>
          <span>{{ t('items.information') }}</span>
        </a>
        <a
          routerLink="products"
          routerLinkActive="!border-eaglo-blue !text-gray-900"
          class="whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 space-x-2 cursor-pointer"
        >
          <i class="fa-regular fa-boxes-stacked"></i>
          <span>{{ t('items.products') }}</span>
        </a>
        <a
          routerLink="invoices"
          routerLinkActive="!border-eaglo-blue !text-gray-900"
          class="whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 space-x-2 cursor-pointer"
        >
          <i class="fa-regular fa-file-invoice"></i>
          <span>{{ t('items.invoices') }}</span>
        </a>
      </nav>
    </div>
  </div>
  <div>
    <router-outlet></router-outlet>
  </div>
</div>
