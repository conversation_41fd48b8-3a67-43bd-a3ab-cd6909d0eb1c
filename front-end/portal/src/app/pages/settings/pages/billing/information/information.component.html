@if(form(); as form) {
  <div *transloco="let t; read: 'pages.settings.billing.information'" [formGroup]="form">
    <!-- Header Section -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl">
      <div class="px-6 py-6 sm:px-8">
        <div class="flex justify-between items-start">
          <div>
            <h2 class="text-xl font-semibold leading-7 text-gray-900">{{ t('title') }}</h2>
            <p class="mt-2 text-sm leading-6 text-gray-600">{{ t('description') }}</p>
          </div>

          <div class="flex-shrink-0">
            <button
              (click)="openMethod()"
              type="button"
              class="btn --blue">
              @if(information()?.pm_type) {
                <i class="fa-regular fa-pen"></i>
              } @else {
                <i class="fa-regular fa-plus"></i>
              }
              <span>{{ t(information()?.pm_type ? 'change_method' : 'add_method') }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Form Section -->
      <div class="border-t border-gray-200 px-6 py-6 sm:px-8">
        <form class="space-y-6" *transloco="let tf; read: 'pages.settings.billing.information'">
          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium leading-6 text-gray-900">
              {{ tf('fields.email') }}
            </label>
            <div class="mt-2">
              <input
                id="email"
                name="email"
                type="email"
                autocomplete="email"
                [formControl]="form.controls.email"
                class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                [placeholder]="tf('placeholders.email')">
            </div>
            @if (form.controls.email.invalid && form.controls.email.touched) {
              <div class="mt-1 text-sm text-red-600">
                @if (form.controls.email.errors?.['required']) {
                  <span>{{ tf('validation.email.required') }}</span>
                }
                @if (form.controls.email.errors?.['email']) {
                  <span>{{ tf('validation.email.invalid') }}</span>
                }
              </div>
            }
          </div>

          <!-- Address Section -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-base font-medium leading-6 text-gray-900 mb-4">{{ tf('address_section_title') }}</h3>

            <!-- Street Address -->
            <div class="space-y-6">
              <div>
                <label for="street" class="block text-sm font-medium leading-6 text-gray-900">
                  {{ tf('fields.street') }}
                </label>
                <div class="mt-2">
                  <input
                    id="street"
                    name="street"
                    type="text"
                    autocomplete="street-address"
                    [formControl]="form.controls.street"
                    class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                    [placeholder]="tf('placeholders.street')">
                </div>
                @if (form.controls.street.invalid && form.controls.street.touched) {
                  <div class="mt-1 text-sm text-red-600">
                    @if (form.controls.street.errors?.['required']) {
                      <span>{{ tf('validation.street.required') }}</span>
                    }
                    @if (form.controls.street.errors?.['minlength']) {
                      <span>{{ tf('validation.street.minlength') }}</span>
                    }
                  </div>
                }
              </div>

              <!-- House Number and Addition -->
              <div class="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-6">
                <div class="sm:col-span-2">
                  <label for="house_number" class="block text-sm font-medium leading-6 text-gray-900">
                    {{ tf('fields.house_number') }}
                  </label>
                  <div class="mt-2">
                    <input
                      id="house_number"
                      name="house_number"
                      type="number"
                      min="1"
                      [formControl]="form.controls.house_number"
                      class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                      [placeholder]="tf('placeholders.house_number')">
                  </div>
                  @if (form.controls.house_number.invalid && form.controls.house_number.touched) {
                    <div class="mt-1 text-sm text-red-600">
                      @if (form.controls.house_number.errors?.['required']) {
                        <span>{{ tf('validation.house_number.required') }}</span>
                      }
                      @if (form.controls.house_number.errors?.['min']) {
                        <span>{{ tf('validation.house_number.min') }}</span>
                      }
                    </div>
                  }
                </div>

                <div class="sm:col-span-4">
                  <label for="house_number_addition" class="block text-sm font-medium leading-6 text-gray-900">
                    {{ tf('fields.house_number_addition') }}
                  </label>
                  <div class="mt-2">
                    <input
                      id="house_number_addition"
                      name="house_number_addition"
                      type="text"
                      [formControl]="form.controls.house_number_addition"
                      class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                      [placeholder]="tf('placeholders.house_number_addition')">
                  </div>
                </div>
              </div>

              <!-- Zipcode and City -->
              <div class="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-6">
                <div class="sm:col-span-2">
                  <label for="zipcode" class="block text-sm font-medium leading-6 text-gray-900">
                    {{ tf('fields.zipcode') }}
                  </label>
                  <div class="mt-2">
                    <input
                      id="zipcode"
                      name="zipcode"
                      type="text"
                      autocomplete="postal-code"
                      [formControl]="form.controls.zipcode"
                      class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                      [placeholder]="tf('placeholders.zipcode')">
                  </div>
                  @if (form.controls.zipcode.invalid && form.controls.zipcode.touched) {
                    <div class="mt-1 text-sm text-red-600">
                      @if (form.controls.zipcode.errors?.['required']) {
                        <span>{{ tf('validation.zipcode.required') }}</span>
                      }
                      @if (form.controls.zipcode.errors?.['pattern']) {
                        <span>{{ tf('validation.zipcode.pattern') }}</span>
                      }
                    </div>
                  }
                </div>

                <div class="sm:col-span-4">
                  <label for="city" class="block text-sm font-medium leading-6 text-gray-900">
                    {{ tf('fields.city') }}
                  </label>
                  <div class="mt-2">
                    <input
                      id="city"
                      name="city"
                      type="text"
                      autocomplete="address-level2"
                      [formControl]="form.controls.city"
                      class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                      [placeholder]="tf('placeholders.city')">
                  </div>
                  @if (form.controls.city.invalid && form.controls.city.touched) {
                    <div class="mt-1 text-sm text-red-600">
                      @if (form.controls.city.errors?.['required']) {
                        <span>{{ tf('validation.city.required') }}</span>
                      }
                      @if (form.controls.city.errors?.['minlength']) {
                        <span>{{ tf('validation.city.minlength') }}</span>
                      }
                    </div>
                  }
                </div>
              </div>

              <!-- Country -->
              <div>
                <label for="country" class="block text-sm font-medium leading-6 text-gray-900">
                  {{ tf('fields.country') }}
                </label>
                <div class="mt-2">
                  <input
                    id="country"
                    name="country"
                    type="text"
                    autocomplete="country-name"
                    [formControl]="form.controls.country"
                    class="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                    [placeholder]="tf('placeholders.country')">
                </div>
                @if (form.controls.country.invalid && form.controls.country.touched) {
                  <div class="mt-1 text-sm text-red-600">
                    @if (form.controls.country.errors?.['required']) {
                      <span>{{ tf('validation.country.required') }}</span>
                    }
                    @if (form.controls.country.errors?.['minlength']) {
                      <span>{{ tf('validation.country.minlength') }}</span>
                    }
                  </div>
                }
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Action Section -->
      <div class="border-t border-gray-200 px-6 py-4 sm:px-8">
        <div class="flex justify-end">
          <button
            (click)="submit()"
            [class.--disabled]="form.invalid"
            [class.--loading]="loading()"
            type="button"
            class="btn --blue">
            <span>{{ 'general.buttons.save' | transloco }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
}
