import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import { DropdownComponent } from '../../../../../components/dropdown/dropdown.component';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { User } from '@api/profile/models/user.interface';
import { UserService } from '@api/profile/services/user.service';
import { HotToastService } from '@ngxpert/hot-toast';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  tap,
} from 'rxjs';
import { genericToastError } from '@app/helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PaginationComponent } from '../../../../../components/pagination/pagination.component';
import { RouterLink } from '@angular/router';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-index',
  imports: [
    DropdownComponent,
    TranslocoPipe,
    TranslocoDirective,
    PaginationComponent,
    RouterLink,
    ReactiveFormsModule,
  ],
  templateUrl: './index.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IndexComponent implements OnInit {
  public response!: PaginatedResponse<User>;
  public loading: boolean = false;

  public search: FormControl<string | null> = new FormControl(null);

  private userService = inject(UserService);
  private toastService = inject(HotToastService);
  private translocoService = inject(TranslocoService);
  private destroyRef = inject(DestroyRef);
  private changeDetectorRef = inject(ChangeDetectorRef);

  public ngOnInit(): void {
    this.loadUsers();
    this.listenToSearch();
  }

  public loadUsers(page: number = 1): void {
    if (this.loading) {
      return;
    }

    this.loading = true;
    this.changeDetectorRef.detectChanges();

    this.userService
      .index({ page, search: this.search.value })
      .pipe(
        filter((response): response is PaginatedResponse<User> => true),
        tap((response) => {
          this.response = response;
          this.loading = false;
          this.changeDetectorRef.detectChanges();
        }),
        catchError((err) => {
          this.loading = false;
          genericToastError(this.toastService, this.translocoService);
          this.changeDetectorRef.detectChanges();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private listenToSearch(): void {
    this.search.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap(() => {
          this.loadUsers();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
