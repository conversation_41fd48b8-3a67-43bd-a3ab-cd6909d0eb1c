<div *transloco="let t; read: 'pages.settings.users.index'" class="space-y-4">
  <div class="flex justify-between items-center">
    <div class="form-group !w-64">
      <input [formControl]="search" [placeholder]="'general.search' | transloco">
    </div>
    <a routerLink="detail" class="btn">
      <i class="fa-regular fa-plus"></i>
      <span>{{ t('add') }}</span>
    </a>
  </div>
  <table class="min-w-full divide-y divide-gray-200 rounded overflow-hidden">
    <thead class="bg-gray-50">
      <tr>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"></th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.name') }}</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.email') }}</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.role') }}</th>
        <th scope="col" class="px-6 py-3"></th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      @if(response && !loading && response.data.length > 0) {
        @for(user of response.data; track $index) {
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              @if(user.image_url) {
                <img [src]="user.image_url" class="h-12 w-12 rounded-full">
              } @else {
                <div class="h-12 w-12 rounded-full bg-eaglo-gray/30"></div>
              }
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.firstname }} {{ user.lastname }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.email }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ 'enums.user-role.' + user.role | transloco }}</td>
            <td class="px-6 py-4 whitespace-nowrap mr-0 ml-auto">
              <components-dropdown>
                <a [routerLink]="['detail',user.id]" class="btn-dropdown">
                  <i class="fa-regular fa-pen"></i>
                  <span>{{  'general.buttons.edit' | transloco }}</span>
                </a>
              </components-dropdown>
            </td>
          </tr>
        }
      } @else if (loading) {
        <tr>
          <td colspan="4">
            <div class="flex items-center justify-center h-20">
              <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-3xl"></i>
            </div>
          </td>
        </tr>
      } @else if(!loading && !!response && response.data.length === 0) {
        <tr>
          <td colspan="4" class="text-center h-20">{{ 'general.empty_placeholder' | transloco }}</td>
        </tr>
      }
      <!-- More rows as needed -->
    </tbody>
  </table>

  <components-pagination [response]="response" class="block"></components-pagination>
</div>