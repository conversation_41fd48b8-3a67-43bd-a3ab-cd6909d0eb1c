<div *transloco="let t; read: 'pages.settings.users.detail'">
  <div class="w-full bg-white shadow rounded-lg p-8 max-w-2xl min-h-96 h-full">
    @if(!loading() && form) {
      <form [formGroup]="form" class="grid grid-cols-1 md:grid-cols-2 gap-y-8 items-start ">
        <div class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="form-group">
            <label>{{ t('inputs.firstname') }}</label>
            <input [formControl]="form.controls.firstname" type="text">
          </div>
          <div class="form-group">
            <label>{{ t('inputs.lastname') }}</label>
            <input [formControl]="form.controls.lastname" type="text">
          </div>
          <div class="form-group md:col-span-2">
            <label>{{ t('inputs.role') }}</label>
            <select [formControl]="form.controls.role" class="w-full">
              @for(role of userRoles; track $index) {
                <option [ngValue]="role">{{ 'enums.user-role.' + role | transloco }}</option>
              }
            </select>
          </div>
          <div class="form-group md:col-span-2">
            <label>{{ t('inputs.email') }}</label>
            <input [formControl]="form.controls.email" type="text">
          </div>
        </div>
        <div class="md:col-span-2 space-y-6">
          @if(imagePreview() || user?.image_url) {
            <img [src]="imagePreview() ?? user?.image_url" alt="Preview" class="h-32 w-32 object-cover rounded-full border border-gray-200 shadow" />  
          } @else {
            <div class="h-32 w-32 object-cover rounded-full border border-gray-200 shadow"></div>
          }
          <div class="form-group flex flex-col gap-2 items-center md:items-end col-span-3">
            <label class="self-start md:self-end">{{ t('inputs.image') }}</label>
            <input type="file" accept="image/*" (change)="onImageChange($event)">
          </div>
          <div class="md:col-span-2 flex items-center justify-end">
              <button (click)="submit()" [class.--loading]="saving()" class="btn">{{ 'general.buttons.save' | transloco }} </button>
          </div>
        </div>
      </form>
    } @else {
      <div class="flex items-center justify-center h-full w-full">
        <i class="fa-duotone fa-solid fa-spinner-third text-4xl text-eaglo-blue animate-spin"></i>
      </div>
    }
  </div>
</div>
