import { Component, DestroyRef, inject, signal } from '@angular/core';
import { catchError, Subject, tap } from 'rxjs';
import {
  genericToastError,
  genericToastSuccess,
} from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Email } from '@api/email/models/email.interface';
import { EmailService } from '@api/email/services/email.service';
import { ModalComponent } from '@components/modal/modal.component';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';

@Component({
  selector: 'app-delete',
  templateUrl: './delete.component.html',
  standalone: true,
  imports: [TranslocoDirective, ModalComponent, TranslocoPipe],
})
export class DeleteComponent {
  public email!: Email;
  public loading = signal<boolean>(false);

  public close$: Subject<boolean> = new Subject<boolean>();

  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);
  private emailService = inject(EmailService);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.email || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.emailService
      .delete(this.email)
      .pipe(
        tap(() => {
          this.loading.set(false);
          genericToastSuccess(this.toastService, this.translocoService);
          this.close(true);
        }),
        catchError((err) => {
          this.loading.set(false);
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
