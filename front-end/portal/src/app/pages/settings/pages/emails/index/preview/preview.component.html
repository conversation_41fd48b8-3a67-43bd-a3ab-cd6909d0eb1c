<components-modal *transloco="let t; read: 'pages.settings.emails.index.preview'" (close$)="close()">
  <div>
    <h2 class="text-lg font-medium">{{ t('title') }}</h2>
    <p>{{ t('description') }}</p>

    <div class="flex justify-end space-x-4 pt-4">
      <button (click)="close()" type="button" class="link">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.loading]="loading()" type="button" class="btn --blue">{{ 'general.buttons.confirm' | transloco }}</button>
    </div>
  </div>
</components-modal>
