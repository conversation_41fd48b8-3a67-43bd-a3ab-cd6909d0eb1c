import {
  Component,
  DestroyRef,
  inject,
  Input,
  NgZone,
  OnInit,
  signal,
} from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Email } from '@api/email/models/email.interface';
import { Editor, grapesjs } from 'grapesjs';
import { EmailService } from '@api/email/services/email.service';
import { HotToastService } from '@ngxpert/hot-toast';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { Router, RouterLink } from '@angular/router';
import { EmailRequest } from '@api/email/requests/email.request';
import { catchError, tap } from 'rxjs';
import {
  genericToastError,
  genericToastSuccess,
} from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { exportEditorToHtml } from '@packages/email/export-editor-to-html';
import emailPlugin from '@packages/email';
import { KeyValuePipe, NgClass } from '@angular/common';

enum Tab {
  GENERAL = 'GENERAL',
  TEMPLATE = 'TEMPLATE',
}

interface Form {
  cc_sender: FormControl<boolean>;
  cc: FormArray<FormControl<string | null>>;
  subject: FormControl<string | null>;
}

@Component({
  selector: 'app-detail',
  imports: [
    ReactiveFormsModule,
    TranslocoPipe,
    TranslocoDirective,
    NgClass,
    KeyValuePipe,
    RouterLink,
  ],
  templateUrl: './detail.component.html',
})
export class DetailComponent implements OnInit {
  @Input({ alias: 'id' }) emailId?: string;
  public email = signal<Email | null>(null);
  public loading = signal<boolean>(false);
  public saveLoading = signal<boolean>(false);
  public showLegend = signal<boolean>(false);
  public activeTab = signal<Tab>(Tab.TEMPLATE);
  public form = signal<FormGroup<Form> | null>(null);
  public submitted = signal<boolean>(false);
  public editor?: Editor;

  public readonly tabs = Tab;

  private emailService = inject(EmailService);
  private toastService = inject(HotToastService);
  private translocoService = inject(TranslocoService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private ngZone = inject(NgZone);

  public ngOnInit(): void {
    this.loadEmail();
  }

  public navigateTab(tab: Tab): void {
    this.activeTab.set(tab);
  }

  public submit(): void {
    this.submitted.set(true);
    const form = this.form();
    const email = this.email();

    if (this.saveLoading() || !email || !form || form.invalid) {
      console.log('test');
      form?.markAllAsTouched();
      return;
    }

    let html = email?.html;

    if (this.editor) {
      html = exportEditorToHtml(this.editor);
    }

    const body: EmailRequest = {
      html: html ?? '',
      cc_sender: form.controls.cc_sender.value,
      cc: form.controls.cc.value.filter((email) => !!email) as string[],
      subject: form.controls.subject.value as string,
    };

    this.saveLoading.set(true);

    this.emailService
      .update(email, body)
      .pipe(
        tap(() => {
          this.saveLoading.set(false);
          genericToastSuccess(this.toastService, this.translocoService);
          this.router.navigate(['settings', 'emails']);
        }),
        catchError((err) => {
          this.saveLoading.set(false);
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  public toggleShowLegend(): void {
    this.showLegend.update((value) => !value);
  }

  public addCcEmail(email: string | null = null): void {
    this.form()?.controls.cc.push(new FormControl(email, [Validators.email]));
  }

  public removeCcEmail(index: number): void {
    this.form()?.controls.cc.removeAt(index);
  }

  private loadEmail(): void {
    if (!this.emailId) {
      return;
    }

    this.loading.set(true);

    this.emailService
      .show(this.emailId)
      .pipe(
        tap((response) => {
          this.email.set(response.data);
          this.initForm();
          this.initBuilder();
          this.loading.set(false);
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private initForm(): void {
    const form = new FormGroup<Form>({
      cc_sender: new FormControl(this.email()?.cc_sender ?? false, {
        nonNullable: true,
      }),
      cc: new FormArray<FormControl<string | null>>([]),
      subject: new FormControl(this.email()?.subject ?? null, [
        Validators.required,
      ]),
    });

    if ((this.email()?.cc?.length ?? 0) > 0) {
      this.email()?.cc?.forEach((email) => this.addCcEmail(email));
    } else {
      this.addCcEmail();
    }

    this.form.set(form);
  }

  private initBuilder(): void {
    const blocks: string[] = [];

    if (
      this.email()?.event ===
      'Packages\\Events\\Events\\Notification\\Recipient\\Link\\ShareLinkEvent'
    ) {
      blocks.push('connections-table');
    }

    this.ngZone.runOutsideAngular(() => {
      setTimeout(() => {
        this.editor = grapesjs.init({
          height: '80.5vh',
          storageManager: false,
          container: '#gjs',
          blockManager: {
            appendTo: '#blocks',
          },
          traitManager: {
            appendTo: '#traits-container',
          },
          selectorManager: {
            appendTo: '#styles-container',
          },
          styleManager: {
            appendTo: '#styles-container',
          },
          layerManager: {
            appendTo: '#layers-container',
          },
          components: this.email()?.html ?? '',
          panels: {
            defaults: [],
          },
          fromElement: true,
          // @ts-ignore
          plugins: [(editor) => emailPlugin(editor, {}, blocks)],
        });

        this.editor.setComponents(this.email()?.html ?? '');
      }, 100);
    });
  }
}
