<div *transloco="let t; read: 'pages.settings.emails.index'" class="space-y-4">
  <div class="flex items-center space-x-2">
      <div class="form-group !w-64">
        <select [formControl]="language">
          <option [ngValue]="null">{{ t('select_language') }}</option>
          @for(language of languages(); track $index) {
            <option [ngValue]="language.code">{{ language.language }}</option>
          }
        </select>
      </div>
      <div class="form-group !w-64">
        <input [formControl]="search" [placeholder]="'general.search' | transloco">
      </div>
  </div>


  <table class="min-w-full divide-y divide-gray-200 rounded overflow-hidden">
    <thead class="bg-gray-50">
      <tr>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.name')}}</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.updated_at')}}</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.updated_by')}}</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.language')}}</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"></th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      @if(response(); as response) {
        @if(response && !loading() && response.data.length > 0) {
          @for(email of response.data; track $index) {
            <tr (click)="navigateToDetail(email)" class="cursor-pointer">
              <td class="px-6 py-4 whitespace-nowrap">{{ email.name }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ email.updated_at | date }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                @if(email.account_id) {
                  {{ email.updated_by?.firstname ?? '' }} {{ email.updated_by?.lastname ?? '' }}
                }
              </td>
              <td class="px-6 py-4 whitespace-nowrap">{{ email.language }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <components-dropdown width="w-44">
                  <button (click)="preview(email)" class="btn-dropdown">
                    <i class="fa-regular fa-paper-plane"></i>
                    <span>{{ t('table.send-preview') }}</span>
                  </button>
                  <button (click)="addLanguage(email)" class="btn-dropdown">
                    <i class="fa-regular fa-plus"></i>
                    <span>{{ t('table.add-language') }}</span>
                  </button>

                  @if (email.language !== 'en') {
                    <button (click)="delete(email)" type="button" class="btn-dropdown">
                      <i class="fa-regular fa-trash-can"></i>
                      <span>{{ 'general.buttons.delete' | transloco }}</span>
                    </button>
                  }
                </components-dropdown>
              </td>
            </tr>
          }
        } @else if (loading()) {
          <tr>
            <td colspan="4">
              <div class="flex items-center justify-center h-20">
                <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-3xl"></i>
              </div>
            </td>
          </tr>
        } @else if(!loading && !!response && response.data.length === 0) {
          <tr>
            <td colspan="4" class="text-center h-20">{{ 'general.empty_placeholder' | transloco }}</td>
          </tr>
        }
      }
    </tbody>
  </table>
  <components-pagination [response]="response()" class="block"></components-pagination>
</div>
