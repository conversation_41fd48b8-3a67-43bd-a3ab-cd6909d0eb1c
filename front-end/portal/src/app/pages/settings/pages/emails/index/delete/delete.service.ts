import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { take, tap } from 'rxjs';
import { Email } from '@api/email/models/email.interface';
import { DeleteComponent } from '@pages/settings/pages/emails/index/delete/delete.component';

@Injectable({
  providedIn: 'root',
})
export class DeleteService {
  constructor(private modalService: ModalService) {}

  public show(email: Email): Promise<boolean> {
    const modal = this.modalService.attach(DeleteComponent);

    modal.componentRef.instance.email = email;

    return new Promise<boolean>((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
