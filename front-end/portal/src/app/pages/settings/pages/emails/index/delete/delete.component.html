<components-modal *transloco="let t; read: 'pages.settings.emails.index.delete'" (close$)="close()">
  <div>
    <h2 class="text-lg font-medium">{{ t('title') }}</h2>
    <p>{{ t('description', { language: email?.language }) }}</p>

    <div class="flex justify-end space-x-4 pt-2">
      <button (click)="close()" type="button" class="link">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.loading]="loading()" type="button" class="btn --danger">{{ 'general.buttons.confirm' | transloco }}</button>
    </div>
  </div>
</components-modal>
