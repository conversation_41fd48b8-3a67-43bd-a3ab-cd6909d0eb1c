import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { Email } from '@api/email/models/email.interface';
import { take, tap } from 'rxjs';
import { AddLanguageComponent } from '@pages/settings/pages/emails/index/add-language/add-language.component';

@Injectable({
  providedIn: 'root',
})
export class AddLanguageService {
  constructor(private modalService: ModalService) {}

  public show(email: Email): Promise<Email | null> {
    const modal = this.modalService.attach(AddLanguageComponent);

    modal.componentRef.instance.email = email;

    return new Promise<Email | null>((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
