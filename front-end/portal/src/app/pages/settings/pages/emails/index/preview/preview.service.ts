import { Injectable } from '@angular/core';
import { ModalService } from '@services/modal.service';
import { Email } from '@api/email/models/email.interface';

import { take, tap } from 'rxjs';
import { PreviewComponent } from '@pages/settings/pages/emails/index/preview/preview.component';

@Injectable({
  providedIn: 'root',
})
export class PreviewService {
  constructor(private modalService: ModalService) {}

  public show(email: Email): void {
    const modal = this.modalService.attach(PreviewComponent);

    modal.componentRef.instance.email = email;

    modal.componentRef.instance.close$
      .pipe(
        tap(() => {
          modal.overlayRef.detach();
        }),
        take(1),
      )
      .subscribe();
  }
}
