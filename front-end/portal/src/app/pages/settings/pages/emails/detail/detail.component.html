<div *transloco="let t; read: 'pages.settings.emails.detail'" class="space-y-4">
  <div class="flex items-center space-x-4 justify-end">
    <a routerLink="/settings/emails" class="link">{{ 'general.buttons.cancel' | transloco}}</a>
    <button (click)="submit()" [class.loading]="saveLoading()" class="btn --blue">{{ 'general.buttons.save' | transloco }}</button>
  </div>

  @if(loading()) {
    <div class="h-full w-full flex items-center justify-center">
      <i class="fa-duotone fa-solid fa-spinner-third animate-spin text-4xl"></i>
    </div>
  } @else {
    <div>
      <div class="border-b border-gray-200">
        <div>
          <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button
              (click)="navigateTab(tabs.TEMPLATE)"
              [ngClass]="{
                '!border-eaglo-blue !text-gray-900': activeTab() === tabs.TEMPLATE
              }"
              class="whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 space-x-2 cursor-pointer"
            >
              <i class="fa-regular fa-file-invoice"></i>
              <span>{{ t('tabs.template') }}</span>
            </button>
            <button
              (click)="navigateTab(tabs.GENERAL)"
              [ngClass]="{
                '!border-eaglo-blue !text-gray-900': activeTab() === tabs.GENERAL
              }"
              class="whitespace-nowrap border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 space-x-2 cursor-pointer"
            >
              <i class="fa-regular fa-circle-info"></i>
              <span>{{ t('tabs.general') }}</span>
            </button>
          </nav>
        </div>
      </div>
    </div>

    @if(activeTab() === tabs.GENERAL && form()) {
      @if(form(); as form) {
        <div class="space-y-6">
          <div class="toggle items-start">
            <input [formControl]="form.controls.cc_sender" type="checkbox" id="cc_sender">
            <label for="cc_sender">{{ t('inputs.cc_sender') | transloco }}</label>
          </div>

          @if(form.controls.cc_sender.value) {
            <div class="w-1/3 space-y-2">
              <div class="flex justify-between items-center">
                <p class="font-semibold">{{ t('inputs.cc')}}</p>
                <button (click)="addCcEmail()">
                  <i class="fa-regular fa-plus text-lg"></i>
                </button>
              </div>

              @for(control of form.controls.cc.controls; track index; let index = $index) {
                <div class="flex items-center space-x-2">
                  <div class="form-group">
                    <input [formControl]="control">
                  </div>

                  <button (click)="removeCcEmail(index)">
                    <i class="fa-regular fa-trash-can text-red-500 text-lg"></i>
                  </button>
                </div>
              }
            </div>
          }

          <div class="form-group-new w-1/3">
            <label class="flex space-x-2 items-center">
              <span>{{ t('inputs.subject') }}</span>
  <!--            <information-icon [text]="t('subject_variable_info')"></information-icon>-->
            </label>
            <input [formControl]="form.controls.subject">
          </div>
        </div>
      }
    }

    @if(activeTab() === tabs.TEMPLATE) {
      <div class="space-y-4">
        <div (click)="toggleShowLegend()" class="flex space-x-4 cursor-pointer items-center">
          <h2 class="font-bold">{{ t('variables') }}</h2>
          <span [class.rotate-180]="showLegend()" class="transition ease-in-out"><i class="fa-solid fa-chevron-down"></i></span>
        </div>

        @if(showLegend()) {
          <div>
            <table class="min-w-full divide-y divide-gray-200 rounded overflow-hidden">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.name') }}</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">{{ t('table.how_to_use') }}</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                  @for(row of (email()?.variables  ?? {})|  keyvalue; track $index) {
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ row.value.name }}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ row.value.value }}</td>
                    </tr>
                  }
              </tbody>
            </table>
          </div>
        }
      </div>
    }

    <div [class.hidden]="activeTab() !== tabs.TEMPLATE" id="email-container" class="pb-8 space-y-4">
      <div class="flex space-x-2">
        <div class="border border-gray-300 rounded-lg overflow-hidden w-1/3 h-[80vh]">
          <div id="panel-views"></div>
          <div class="h-[95%] overflow-y-auto">
            <div id="blocks"></div>
            <div id="styles-container" style="display: none"></div>
            <div id="traits-container" style="display: none"></div>
            <div id="layers-container" style="display: none"></div>
          </div>
        </div>
        <div class="w-2/3 space-y-1">
          <div class="flex justify-between">
            <div id="devices"></div>
            <div id="options"></div>
          </div>
          <div [class.hidden]="activeTab() !== tabs.TEMPLATE" id="gjs" class="">
          </div>
        </div>
      </div>
    </div>
}
</div>
