<components-modal *transloco="let t; read: 'pages.settings.emails.index.add-language'" (close$)="close()">
  <div class="space-y-4">
    <h2 class="text-lg font-medium">{{ t('title') }}</h2>

    @if(form(); as form) {
      <div class="form-group">
        <select [formControl]="form.controls.language">
          <option [ngValue]="null" disabled>{{ 'general.select' | transloco }}</option>
          @for(language of languages(); track $index) {
            <option [ngValue]="language.code">{{ language.language }}</option>
          }
        </select>
        <label>{{ t('language') }}</label>
      </div>
    }

    <div class="flex justify-end space-x-4">
      <button (click)="close()" type="button" class="link">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.loading]="loading()" type="button" class="btn --blue">{{ 'general.buttons.confirm' | transloco }}</button>
    </div>
  </div>
</components-modal>
