import { Component, DestroyRef, inject, signal } from '@angular/core';
import { Email } from '@api/email/models/email.interface';
import { EmailService } from '@api/email/services/email.service';
import { catchError, Subject, tap } from 'rxjs';
import {
  genericToastError,
  genericToastSuccess,
} from '@helpers/generic-toasts';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ModalComponent } from '@components/modal/modal.component';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';

@Component({
  selector: 'app-preview',
  standalone: true,
  imports: [ModalComponent, TranslocoDirective, TranslocoPipe],
  templateUrl: './preview.component.html',
})
export class PreviewComponent {
  public email!: Email;
  public close$: Subject<void> = new Subject();
  public loading = signal<boolean>(false);

  private emailService = inject(EmailService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }

  public submit(): void {
    if (!this.email || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.emailService
      .preview(this.email)
      .pipe(
        tap(() => {
          this.loading.set(false);
          genericToastSuccess(this.toastService, this.translocoService);
          this.close();
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.loading.set(false);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
