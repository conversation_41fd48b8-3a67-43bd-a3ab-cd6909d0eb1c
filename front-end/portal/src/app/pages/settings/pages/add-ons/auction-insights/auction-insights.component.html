<ng-container *transloco="let t; read: 'pages.settings.add-ons.auction-insights'">
  <div class="p-4 bg-white rounded-lg shadow">
    <div (click)="toggleOpen()" class="flex items-center space-x-3 cursor-pointer">
      <div class="flex-shrink-0">
        <div class="w-10 h-10 bg-eaglo-blue/15 rounded flex items-center justify-center">
          <i class="fa-regular fa-chart-column text-eaglo-blue"></i>
        </div>
      </div>
      <div class="flex-1">
        <h3 class="text-base font-medium text-gray-900">{{ t('title') }}</h3>
        <p class="text-sm text-gray-500">{{ t('description') }}</p>
      </div>
      <div class="flex-shrink-0">
        <button
          [ngClass]="{
            'rotate-180': open()
          }"
          class="transition ease-in-out"
        >
          <i class="fa-regular fa-chevron-down"></i>
        </button>
      </div>
    </div>

    @if(open()) {
      <div class="pt-4 divide-y divide-gray-200">
        <div class="pb-4 space-y-4">
          @if(guiding()) {
            @for(entry of steps; track index; let index = $index) {
              <div
                [ngClass]="{
                  'bg-white': steps.indexOf(activeStep()) >= index
                }"
                class="bg-eaglo-gray-100 rounded-lg shadow border border-gray-200 p-4"
              >
                <div class="flex items-start space-x-3">
                  <p class="min-h-6 min-w-6 rounded-full bg-eaglo-blue text-white flex items-center justify-center">{{ index + 1}}</p>
                  <div>
                    <h2 class="font-medium">{{ t('steps.' + entry + '.title') }}</h2>
                    <p class="text-sm">{{ t('steps.' + entry + '.description') }}</p>
                    @if(activeStep() === entry) {
                      @if(asTemplateRef(entry); as template) {
                        <ng-container *ngTemplateOutlet="template"></ng-container>
                      }
                    }
                  </div>
                </div>
              </div>
            }

          }

          <div class="flex space-x-4">
            @if(guiding()) {
              <button (click)="toggleGuiding()" class="btn --small --outline">{{ t('buttons.collapse') }}</button>

              <button (click)="completeGuide()" [class.--disabled]="activeStep() !== step.FINISHED" [class.--loading]="submitting()" class="btn w-full --small">
                <i class="fa-regular fa-circle-check"></i>
                <span>{{ t('buttons.add') }}</span>
              </button>
            } @else {
              <button (click)="toggleGuiding()" class="btn w-full --small">
                <i class="fa-regular fa-circle-play"></i>
                <span>{{ t('buttons.start') }}</span>
              </button>
            }
          </div>
        </div>
        @if(response(); as response) {
          @if(response && response.data.length > 0) {
            <div class="pt-4 space-y-4">
              <p>{{ t('connected-accounts') }}</p>
              @for(account of response.data; track $index) {
                <div class="bg-white rounded-lg shadow border border-gray-200 p-4">
                  <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                      <div class="w-10 h-10 bg-blue-100 rounded flex items-center justify-center">
                        <span class="text-blue-600 font-semibold text-sm">{{ account.name?.charAt(0) ?? ('general.unknown' | transloco ).charAt(0) }}</span>
                      </div>
                    </div>
                    <div class="flex-1">
                      <h3 class="text-base font-medium text-gray-900">{{ account.name ?? ('general.unknown' | transloco ) }}</h3>
                      <p class="text-sm text-gray-500 space-x-1">
                        <span>{{ t('account-id') }}</span>
                        <span>{{ account.external_id }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              }
              <components-pagination [response]="response" (pageChange$)="loadAccounts($event)"></components-pagination>
            </div>
          }
        }
      </div>
    }
  </div>

  @if(form(); as form) {
    <ng-template #SELECT_ACCOUNT>
      <form-connected-select [source]="googleAdAccountSourceCallback" [formControl]="form.controls.ad_account_id" class="block"></form-connected-select>
    </ng-template>
    <ng-template #CREATE_EXPORT>
      <div class="space-y-4">
        @for(entry of [].constructor(10); track $index; let index = $index) {
          <div>
            <h2 class="font-medium">{{ index + 1 }}. {{ t('steps.CREATE_EXPORT.steps.' + index + '.title') }}</h2>
            <p>{{ t('steps.CREATE_EXPORT.steps.' + index + '.description') }}</p>
            @if(index === 0) {
              <a href="https://ads.google.com/aw/insights/auctioninsights" target="_blank" class="link text-eaglo-blue">https://ads.google.com/aw/insights/auctioninsights</a>
            }
          </div>
        }
        <div class="flex justify-end">
          <button (click)="setStep(step.SELECT_DRIVE)" class="btn --small">{{ 'general.buttons.next' | transloco }}</button>
        </div>
      </div>
    </ng-template>
    <ng-template #SELECT_DRIVE>
      <form-connected-select [source]="googleDriveSourceCallback" [formControl]="form.controls.drive_id" class="block"></form-connected-select>
    </ng-template>

  }

</ng-container>
