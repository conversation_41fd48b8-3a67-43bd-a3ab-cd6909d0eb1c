import { inject, Injectable } from '@angular/core';
import { GoogleAccount } from '@api/google/models/google-account.interface';
import { DeleteComponent } from './delete.component';
import { take, tap } from 'rxjs';
import { ModalService } from '@app/services/modal.service';

@Injectable({
  providedIn: 'root',
})
export class DeleteService {
  private modalService = inject(ModalService);

  public show(account: GoogleAccount): Promise<boolean> {
    const modal = this.modalService.attach(DeleteComponent);

    modal.componentRef.instance.account = account;

    return new Promise((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
