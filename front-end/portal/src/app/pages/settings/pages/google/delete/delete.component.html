<components-modal *transloco="let t; read: 'pages.settings.google.delete'" (close$)="close()">
  <div class="space-y-4">
    <h2 class="font-medium text-xl">{{ t('title') }}</h2>
    <p>{{ t('description') }}</p>

    <div class="flex items-center justify-end space-x-4">
      <button (click)="close()" class="btn --outline">{{ 'general.buttons.cancel' | transloco }}</button>
      <button (click)="submit()" [class.--loading]="loading()" type="submit" class="btn --danger">{{ 'general.buttons.delete' | transloco }}</button>
    </div>
  </div>

</components-modal>