<div *transloco="let t; read: 'pages.dashboards.detail.sources'" [formGroup]="form" class="space-y-6">
  <div>
    <h2 class="text-xl font-medium">{{ t('title') }}</h2>
    <p>{{ t('description') }}</p>
  </div>

  <div class="space-y-4">
    <p class="text-sm">{{ t('google_ad_accounts.field') }}</p>
    <div class="space-y-2">
      @for(control of form.controls.google_ad_account_ids.controls; let index = $index; track index) {
        <div class="flex items-center space-x-8">
          <form-connected-select [formControl]="control" [source]="googleAdAccountSourceCallback" class="w-full"></form-connected-select>
          <button (click)="removeControl(form.controls.google_ad_account_ids, index)" type="button" class="h-4 w-4 cursor-pointer">
            <i class="fa-regular fa-trash-can text-red-500"></i>
          </button>
        </div>
      }
    </div>
    <button (click)="addControl(form.controls.google_ad_account_ids)" class="w-full border-dashed border-2 border-gray-300 rounded py-2 space-x-2 cursor-pointer">
      <i class="fa-regular fa-plus"></i>
      <span>{{ t('google_ad_accounts.button') }}</span>
    </button>
  </div>
</div>
