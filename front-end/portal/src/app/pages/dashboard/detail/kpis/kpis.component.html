<div *transloco="let t; read: 'pages.dashboards.detail.kpis'" class="divide-y divide-gray-200 space-y-6">
  @for(kpi of form.controls; track $index) {
    @if({open: true}; as state) {
      <div class="border rounded-lg border-gray-200 space-y-4">
        <div class="flex items-center justify-between hover:bg-gray-100/60 px-4 py-4">
          <button (click)="state.open = !state.open" class="flex items-center space-x-3 cursor-pointer h-full">
            <span [class.rotate-90]="state.open" class="transition block text-gray-400 hover:text-gray-500"><i class="fa-regular fa-chevron-right text-sm"></i></span>
            <span class="text-sm font-medium text-gray-900">{{ t('types.' + kpi.controls.type.value) }}</span>
          </button>
        </div>

        @if(state.open) {
          <div class="space-y-2">
            @for(entry of kpi.controls.entries.controls; let index = $index; track index) {
              <div class="flex items-end space-x-4 px-4">
                <div class="form-group-new">
                  <label>{{ t('unit') }}</label>
                  <select [formControl]="entry.controls.unit">
                    <option [ngValue]="null" disabled>{{ 'general.select' | transloco }}</option>
                    <option [ngValue]="unit.AMOUNT">{{ t('units.amount') }}</option>
                    <option [ngValue]="unit.PERCENTAGE">{{ t('units.percentage') }}</option>
                  </select>
                </div>
                <div class="form-group-new">
                  <label>{{ t('target') }}</label>
                  <input [formControl]="entry.controls.target" type="number">
                </div>
                <div class="form-group-new">
                  <label>{{ t('year') }}</label>
                  <nz-date-picker [formControl]="entry.controls.year" nzMode="year"></nz-date-picker>
                </div>

                <button (click)="removeEntry(kpi, index)" class="p-2 cursor-pointer">
                  <i class="fa-regular fa-trash-can text-red-500"></i>
                </button>
              </div>
            }
          </div>

          <div class="px-4 pb-2">
            <button (click)="addEntry(kpi)" class="w-full border-dashed border-2 border-gray-300 rounded py-2 space-x-2 cursor-pointer">
              <i class="fa-regular fa-plus"></i>
              <span>{{ t('add') }}</span>
            </button>
          </div>
        }
      </div>
    }
  }
</div>
