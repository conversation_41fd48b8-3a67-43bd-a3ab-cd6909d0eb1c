import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { KpiEntryForm, KpiForm } from '../dashboard.interface';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { getYear } from 'date-fns';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { WidgetKpiUnit } from '@api/dashboard/enums/widget-kpi-unit.enum';

@Component({
  selector: 'dashboard-detail-kpis',
  imports: [
    TranslocoDirective,
    ReactiveFormsModule,
    TranslocoPipe,
    NzDatePickerModule,
  ],
  templateUrl: './kpis.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KpisComponent {
  @Input({ required: true }) form!: FormArray<FormGroup<KpiForm>>;

  public readonly unit = WidgetKpiUnit;

  public addEntry(kpi: FormGroup<KpiForm>): void {
    const entry = new FormGroup<KpiEntryForm>({
      target: new FormControl(null, [Validators.required]),
      year: new FormControl(new Date(), [Validators.required]),
      unit: new FormControl(null, [Validators.required]),
    });

    kpi.controls.entries.push(entry);
  }

  public removeEntry(kpi: FormGroup<KpiForm>, index: number): void {
    kpi.controls.entries.removeAt(index);
  }
}
