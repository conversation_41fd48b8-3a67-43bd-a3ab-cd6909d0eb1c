import { Form, FormArray, FormControl, FormGroup } from '@angular/forms';
import { PageType } from '@api/dashboard/enums/page-type.enum';
import { SectionType } from '@api/dashboard/enums/section-type.enum';
import { WidgetKpiUnit } from '@api/dashboard/enums/widget-kpi-unit.enum';
import { WidgetType } from '@api/dashboard/enums/widget-type.enum';
import { SelectOption } from '@app/interfaces/select-option.interface';
import { DashboardBusinessType } from '@api/dashboard/enums/dashboard-business-type.enum';

export interface DashboardForm {
  general: FormGroup<GeneralForm>;
  pages: FormArray<FormGroup<PageForm>>;
  sources: FormGroup<SourcesForm>;
  recipient: FormGroup<RecipientForm>;
  kpis: FormArray<FormGroup<KpiForm>>;
}

export interface GeneralForm {
  name: FormControl<string | null>;
  user_id: FormControl<SelectOption<number> | null>;
  business_type: FormControl<DashboardBusinessType | null>;
}

export interface SourcesForm {
  google_ad_account_ids: FormArray<FormControl<SelectOption<number> | null>>;
}

export interface PageForm {
  enabled: FormControl<boolean>;
  type: FormControl<PageType>;
  sections: FormArray<FormGroup<SectionForm>>;
}

export interface SectionForm {
  enabled: FormControl<boolean>;
  type: FormControl<SectionType>;
  placement: FormControl<number | null>;
}

export interface RecipientForm {
  company: FormControl<string | null>;
}

export interface KpiForm {
  type: FormControl<WidgetType>;
  entries: FormArray<FormGroup<KpiEntryForm>>;
}

export interface KpiEntryForm {
  year: FormControl<Date | null>;
  target: FormControl<number | null>;
  unit: FormControl<WidgetKpiUnit | null>;
}
