<div *transloco="let t; read: 'pages.dashboards.detail.pages'" class="space-y-6">
  @for(page of form.controls.pages.controls; track $index) {
    @if({open: false}; as state) {
      <div class="border rounded-lg border-gray-200">
        <div class="flex items-center justify-between hover:bg-gray-100/60 p-4">
          <button (click)="state.open = !state.open" class="flex items-center space-x-3 cursor-pointer">
            <span [class.rotate-90]="state.open" class="transition block text-gray-400 hover:text-gray-500"><i class="fa-regular fa-chevron-right text-sm"></i></span>
            <span class="text-sm font-medium text-gray-900">{{ t('pages.' + page.controls.type.value) }}</span>
          </button>
          <label class="relative inline-flex items-center cursor-pointer">
            <input [formControl]="page.controls.enabled" type="checkbox" class="sr-only peer">
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-eaglo-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-eaglo-black"></div>
          </label>
        </div>

        @if(state.open) {
          <div cdkDropList (cdkDropListDropped)="drop($event, page.controls.type.value)" class="drag-drop-container">
            @for(section of page.controls.sections.controls; track $index) {
              <div cdkDrag class="drag-drop-item !pr-4 !pl-8 py-2">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="cursor-move"><i class="fa-regular fa-grip-dots-vertical"></i></div>
                    <label>{{ t('sections.' + section.controls.type.value) }}</label>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input [formControl]="section.controls.enabled" [id]="section.controls.type.value" type="checkbox" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-eaglo-black/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-eaglo-black"></div>
                  </label>
                </div>
              </div>
            }
        </div>
      }
      </div>
    }
  }
</div>
