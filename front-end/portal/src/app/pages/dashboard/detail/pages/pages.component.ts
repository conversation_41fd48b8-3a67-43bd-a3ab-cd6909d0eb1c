import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DashboardForm, PageForm } from '../dashboard.interface';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import {
  CdkDrag,
  CdkDragDrop,
  CdkDropList,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { PageType } from '@api/dashboard/enums/page-type.enum';

@Component({
  selector: 'dashboard-detail-pages',
  imports: [ReactiveFormsModule, TranslocoDirective, CdkDropList, CdkDrag],
  templateUrl: './pages.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PagesComponent {
  @Input({ required: true }) form!: FormGroup<DashboardForm>;

  drop(event: CdkDragDrop<string[]>, pageType: PageType) {
    if (!this.form) {
      return;
    }

    const sections = this.form.controls.pages.controls
      .filter((page) => page.controls.type.value === pageType)
      .at(0)?.controls.sections.controls;

    if (!sections) {
      return;
    }

    moveItemInArray(sections, event.previousIndex, event.currentIndex);
  }
}
