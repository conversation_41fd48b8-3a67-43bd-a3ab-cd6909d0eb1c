import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { PageType } from '@api/dashboard/enums/page-type.enum';
import { SectionType } from '@api/dashboard/enums/section-type.enum';
import { DashboardService } from '@api/dashboard/services/dashboard.service';
import {
  genericToastError,
  genericToastSuccess,
} from '@app/helpers/generic-toasts';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import {
  DashboardForm,
  GeneralForm,
  KpiEntryForm,
  KpiForm,
  PageForm,
  RecipientForm,
  SectionForm,
  SourcesForm,
} from './dashboard.interface';
import { PagesComponent } from './pages/pages.component';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  tap,
} from 'rxjs';
import { GeneralComponent } from './general/general.component';
import { Router, RouterLink } from '@angular/router';
import {
  DashboardGeneralRequest,
  DashboardKpiRequest,
  DashboardPageRequest,
  DashboardRequest,
  DashboardSectionRequest,
} from '@api/dashboard/requests/dashboard.request';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { SourcesComponent } from './sources/sources.component';
import { DashboardSourceType } from '@api/dashboard/enums/dashboard-source-type.enum';
import { SelectOption } from '@app/interfaces/select-option.interface';
import { RecipientComponent } from './recipient/recipient.component';
import { Section } from '@api/dashboard/models/section.interface';
import { WidgetType } from '@api/dashboard/enums/widget-type.enum';
import { WidgetKpi } from '@api/dashboard/models/widget-kpi.interface';
import { KpisComponent } from './kpis/kpis.component';
import { getYear } from 'date-fns';
import { uniqueYearValidator } from '@app/validators/unique-year-validator';
import { DashboardBusinessType } from '@api/dashboard/enums/dashboard-business-type.enum';

enum Step {
  GENERAL = 'GENERAL',
  RECIPIENT = 'RECIPIENT',
  SOURCES = 'SOURCES',
  PAGES = 'PAGES',
  KPIS = 'KPIS',
}

@Component({
  selector: 'app-detail',
  imports: [
    TranslocoDirective,
    ReactiveFormsModule,
    TranslocoPipe,
    PagesComponent,
    GeneralComponent,
    RouterLink,
    SourcesComponent,
    RecipientComponent,
    KpisComponent,
  ],
  templateUrl: './detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailComponent implements OnInit {
  @Input('id') dashboardId!: number;
  public loading = signal<boolean>(false);
  public saving = signal<boolean>(false);
  public nextStepAllowed = signal<boolean>(false);
  public form = signal<FormGroup<DashboardForm> | null>(null);
  public currentStep = signal<Step>(Step.GENERAL);
  public currentIndex = signal<number>(0);
  public dashboard = signal<Dashboard | null>(null);

  public readonly step = Step;
  public steps = signal<Step[]>([
    Step.GENERAL,
    Step.RECIPIENT,
    Step.SOURCES,
    Step.PAGES,
  ]);

  private dashboardService = inject(DashboardService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);

  public ngOnInit(): void {
    this.loadDashboard();
  }

  public changeStep(step: Step): void {
    this.currentStep.set(step);
    this.currentIndex.set(Object.values(Step).indexOf(step));
  }

  public nextStep(): void {
    if (
      this.currentIndex() + 1 === this.steps.length ||
      !this.nextStepAllowed()
    ) {
      return;
    }

    this.currentIndex.update((index) => index + 1);

    const step = this.steps()[this.currentIndex()];
    this.currentStep.set(step);
  }

  public previousStep(): void {
    if (this.currentIndex() === 0) {
      return;
    }

    this.currentIndex.update((index) => index - 1);
    const step = this.steps()[this.currentIndex()];
    this.currentStep.set(step);
  }

  public submit(): void {
    const form = this.form();

    if (this.saving() || !form || form.invalid) {
      form?.markAllAsTouched();
      return;
    }

    this.saving.set(true);

    const pages = form.controls.pages.controls
      .filter((page) => page.controls.enabled.value)
      .map((page) => {
        const sections = page.controls.sections.controls
          .filter((section) => section.controls.enabled.value)
          .map(
            (section) =>
              ({
                type: section.controls.type.value,
              }) as DashboardSectionRequest,
          );

        return {
          type: page.controls.type.value,
          sections,
        } as DashboardPageRequest;
      });

    const body: DashboardRequest = {
      general: {
        name: form.controls.general.controls.name.value as string,
        user_id: form.controls.general.controls.user_id.value?.value as number,
        business_type: form.controls.general.controls.business_type
          .value as DashboardBusinessType,
      },
      pages,
      sources: {
        google_ad_account_ids:
          form.controls.sources.controls.google_ad_account_ids.controls
            .filter((control) => !!control.value?.value)
            .map((control) => control.value?.value) as number[],
      },
      recipient: {
        company: form.controls.recipient.controls.company.value as string,
      },
      kpis: form.controls.kpis.controls.map((kpi) => ({
        type: kpi.controls.type.value as WidgetType,
        entries: kpi.controls.entries.controls.map((entry) => ({
          target: entry.controls.target.value as number,
          year: getYear(entry.controls.year.value as Date),
          unit: entry.controls.unit.value,
        })),
      })) as DashboardKpiRequest[],
    };

    let service = this.dashboardService.store(body);

    const dashboard = this.dashboard();

    if (dashboard) {
      service = this.dashboardService.update(dashboard, body);
    }

    service
      .pipe(
        tap(() => {
          this.saving.set(false);
          genericToastSuccess(this.toastService, this.translocoService);
          this.router.navigate(['dashboards']);
        }),
        catchError((err) => {
          this.saving.set(false);
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private async initForm(): Promise<void> {
    const configuration = await this.dashboardService
      .configuration()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .toPromise();

    if (!configuration) {
      genericToastError(this.toastService, this.translocoService);
      return;
    }

    const dashboard = this.dashboard();

    const form = new FormGroup<DashboardForm>({
      general: new FormGroup<GeneralForm>({
        name: new FormControl(dashboard?.name ?? null, [Validators.required]),
        user_id: new FormControl(
          !!dashboard?.user ? { value: dashboard.user.id, label: '' } : null,
          [Validators.required],
        ),
        business_type: new FormControl(dashboard?.business_type ?? null, [
          Validators.required,
        ]),
      }),
      pages: new FormArray<FormGroup<PageForm>>([]),
      sources: new FormGroup<SourcesForm>({
        google_ad_account_ids: new FormArray<
          FormControl<SelectOption<number> | null>
        >([]),
      }),
      recipient: new FormGroup<RecipientForm>({
        company: new FormControl(dashboard?.recipient?.company ?? null, [
          Validators.required,
        ]),
      }),
      kpis: new FormArray<FormGroup<KpiForm>>([]),
    });

    this.getMappedFormSources(DashboardSourceType.GOOGLE_AD_ACCOUNT)?.forEach(
      (source) => {
        form.controls.sources.controls.google_ad_account_ids.push(
          new FormControl<SelectOption<number>>(source),
        );
      },
    );

    Object.keys(configuration.data).forEach((page) => {
      const existingPage = dashboard?.pages
        ?.filter((existing) => existing.type === page)
        .at(0);

      const pageForm = new FormGroup<PageForm>({
        enabled: new FormControl(dashboard ? !!existingPage : true, {
          nonNullable: true,
        }),
        type: new FormControl(page as PageType, { nonNullable: true }),
        sections: new FormArray<FormGroup<SectionForm>>([]),
      });

      Object.keys(configuration.data[page]).forEach((section) => {
        const existingSection = existingPage?.sections
          ?.filter((existing) => existing.type === section)
          .at(0);

        const sectionForm = new FormGroup<SectionForm>({
          enabled: new FormControl(dashboard ? !!existingSection : true, {
            nonNullable: true,
          }),
          type: new FormControl(section as SectionType, { nonNullable: true }),
          placement: new FormControl(existingSection?.placement ?? null, {
            nonNullable: true,
          }),
        });

        const setKpisSettings = (enabled: boolean) => {
          Object.keys(configuration.data[page][section as SectionType])
            .filter(
              (widget) =>
                !!configuration.data[page][section as SectionType][
                  widget as WidgetType
                ].has_kpis,
            )
            .forEach((widget) => {
              const kpis = existingSection?.widgets
                ?.filter(
                  (existingWidget) =>
                    existingWidget.type === (widget as WidgetType),
                )
                .at(0)?.kpis;

              this.toggleKpisForWidget(
                form,
                enabled,
                widget as WidgetType,
                kpis ?? null,
              );
            });
        };

        if (existingSection) {
          setKpisSettings(true);
        }

        sectionForm.controls.enabled.valueChanges
          .pipe(
            tap((value) => {
              setKpisSettings(value);

              if (!value) {
                return;
              }

              pageForm.controls.enabled.setValue(true, {
                onlySelf: true,
                emitEvent: false,
              });
            }),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();

        pageForm.controls.sections.push(sectionForm);
      });

      pageForm.controls.sections.controls.sort((a, b) => {
        if (a.controls.placement.value === null) return 1;

        if (b.controls.placement.value === null) return -1;

        return a.controls.placement.value - b.controls.placement.value;
      });

      pageForm.controls.enabled.valueChanges
        .pipe(
          tap((value) => {
            pageForm.controls.sections.controls.forEach((section) =>
              section.controls.enabled.setValue(value),
            );
          }),
          takeUntilDestroyed(this.destroyRef),
        )
        .subscribe();

      form.controls.pages.push(pageForm);
    });

    form.valueChanges
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        tap((value) => {
          this.nextStepAllowed.set(this.isNextStepAllowed());
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.form.set(form);

    this.nextStepAllowed.set(this.isNextStepAllowed());
  }

  private isNextStepAllowed(): boolean {
    const form = this.form();
    if (!form) {
      return false;
    }

    if (this.currentStep() === Step.GENERAL) {
      return form?.controls.general.valid;
    }

    if (this.currentStep() === Step.KPIS) {
      return form?.controls.kpis.valid;
    }

    return true;
  }

  private loadDashboard(): void {
    if (!this.dashboardId) {
      this.initForm();
      return;
    }

    this.loading.set(true);

    this.dashboardService
      .show(this.dashboardId)
      .pipe(
        tap((response) => {
          this.dashboard.set(response.data);
          this.initForm();
          this.loading.set(false);
        }),
        catchError((err) => {
          genericToastError(this.toastService, this.translocoService);
          this.loading.set(false);
          this.initForm();
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private getMappedFormSources(
    type: DashboardSourceType,
  ): SelectOption<number>[] | null {
    return (
      this.dashboard()
        ?.sources?.filter((source) => source.source_type === type)
        .map(
          (source) =>
            ({ value: source.source_id, label: '' }) as SelectOption<number>,
        ) ?? null
    );
  }

  private toggleKpisForWidget(
    form: FormGroup<DashboardForm>,
    enabled: boolean,
    type: WidgetType,
    kpis: WidgetKpi[] | null = null,
  ): void {
    if (!enabled) {
      const filtered = form.controls.kpis.controls.filter(
        (group) => group.controls.type.value !== type,
      );

      form.controls.kpis.clear();

      filtered.forEach((entry) => form.controls.kpis.push(entry));

      if (filtered.length === 0 && this.steps().includes(Step.KPIS)) {
        this.steps().pop();
      }
      return;
    }

    const group = new FormGroup<KpiForm>({
      type: new FormControl(type, { nonNullable: true }),
      entries: new FormArray<FormGroup<KpiEntryForm>>(
        [],
        [uniqueYearValidator()],
      ),
    });

    if (kpis) {
      kpis.forEach((kpi) => {
        const entry = new FormGroup<KpiEntryForm>({
          target: new FormControl(kpi.target, [Validators.required]),
          year: new FormControl(new Date(kpi.year, 0, 1), [
            Validators.required,
          ]),
          unit: new FormControl(kpi.unit, [Validators.required]),
        });

        group.controls.entries.push(entry);
      });
    } else {
      const entry = new FormGroup<KpiEntryForm>({
        target: new FormControl(null, [Validators.required]),
        year: new FormControl(new Date(), [Validators.required]),
        unit: new FormControl(null, [Validators.required]),
      });

      group.controls.entries.push(entry);
    }

    form.controls.kpis.push(group);

    if (!this.steps().includes(Step.KPIS)) {
      this.steps().push(Step.KPIS);
    }
  }
}
