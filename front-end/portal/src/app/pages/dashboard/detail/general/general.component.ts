import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  Input,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DashboardForm, GeneralForm } from '../dashboard.interface';
import { TranslocoDirective, TranslocoPipe } from '@jsverse/transloco';
import { combineLatest, map, Observable } from 'rxjs';
import { SelectOption } from '@app/interfaces/select-option.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { GoogleAdAccountService } from '@api/google/services/google-ad-account.service';
import { UserService } from '@api/profile/services/user.service';
import { User } from '@api/profile/models/user.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ConnectedSelectComponent } from '../../../../components/form-inputs/connected-select/connected-select.component';
import { DashboardBusinessType } from '@api/dashboard/enums/dashboard-business-type.enum';
import { KeyValuePipe, NgClass } from '@angular/common';

@Component({
  selector: 'dashboard-detail-general',
  imports: [
    TranslocoDirective,
    ReactiveFormsModule,
    ConnectedSelectComponent,
    KeyValuePipe,
    TranslocoPipe,
    NgClass,
  ],
  templateUrl: './general.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GeneralComponent {
  @Input({ required: true }) form!: FormGroup<GeneralForm>;

  private userService = inject(UserService);
  private destroyRef = inject(DestroyRef);

  public readonly businessType = DashboardBusinessType;

  public userSourceCallback = (
    page: number,
    filters: { [key: string]: any },
    search: string | null | undefined,
  ): Observable<PaginatedResponse<SelectOption<number>>> => {
    const mapUserToSelectOption = (user: User): SelectOption<number> => ({
      value: user.id,
      label: user.firstname + ' ' + user.lastname,
      description: user.email,
    });

    const index = this.userService.index({ page, search }).pipe(
      map(
        (response): PaginatedResponse<SelectOption<number>> =>
          ({
            ...response,
            data: response.data.map((user) => mapUserToSelectOption(user)),
          }) as PaginatedResponse<SelectOption<number>>,
      ),
      takeUntilDestroyed(this.destroyRef),
    );

    const userId = this.form?.controls.user_id.value?.value;

    if (!search && userId) {
      combineLatest([index, this.userService.show(userId)]).pipe(
        map(([index, show]): PaginatedResponse<SelectOption<number>> => {
          const userAlreadyInIndex =
            index.data.map((user) => user.value).indexOf(show.data.id) !== -1;

          if (!userAlreadyInIndex) {
            index.data.push(mapUserToSelectOption(show.data));
          }
          return index;
        }),
      );
    }

    return index;
  };
}
