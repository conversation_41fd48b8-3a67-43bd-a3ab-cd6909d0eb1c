<div *transloco="let t; read: 'pages.dashboards.detail.general'" [formGroup]="form" class="space-y-6">
  <div>
    <h2 class="text-xl font-medium">{{ t('title') }}</h2>
    <p>{{ t('description') }}</p>
  </div>

  <div class="form-group-new">
    <label>{{ t('fields.name.title') }}</label>
    <input [formControl]="form.controls.name">
    <p class="pt-2 text-sm">{{ t('fields.name.description') }}</p>
  </div>

  <div>
    <form-connected-select [formControl]="form.controls.user_id" [source]="userSourceCallback" [label]="t('fields.user.title')"></form-connected-select>
    <p class="text-sm pt-2">{{ t('fields.user.description') }}</p>
  </div>

  <div class="form-group-new">
    <label>{{ t('fields.business_type.title') }}</label>

    <div class="grid grid-cols-3 gap-6">
      @for(type of businessType | keyvalue; track $index) {
        <label
          [for]="type.value"
          [ngClass]="{
             'bg-eaglo-blue/10 !border-eaglo-blue text-eaglo-blue font-medium': form.controls.business_type.value === type.value,
          }"
          class="border border-gray-300 rounded text-center py-2 cursor-pointer"
        >
          {{ 'enums.business-type.' + type.value | transloco }}
        </label>
        <input [formControl]="form.controls.business_type" [id]="type.value" [value]="type.value" type="radio" class="hidden">
      }
    </div>
  </div>
</div>
