import { inject, Injectable } from '@angular/core';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { ModalService } from '@app/services/modal.service';
import { ShareComponent } from './share.component';
import { take, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ShareService {
  private modalService = inject(ModalService);

  public show(dashboard: Dashboard): void {
    const modal = this.modalService.attach(ShareComponent);

    modal.componentRef.instance.dashboard = dashboard;

    modal.componentRef.instance.close$
      .pipe(
        tap(() => modal.overlayRef.detach()),
        take(1),
      )
      .subscribe();
  }
}
