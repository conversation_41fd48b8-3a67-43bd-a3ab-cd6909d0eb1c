import {
  ChangeDetectionStrategy,
  Component,
  inject,
  signal,
} from '@angular/core';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { Subject } from 'rxjs';
import { ModalComponent } from '../../../../components/modal/modal.component';
import { TranslocoDirective } from '@jsverse/transloco';
import { Clipboard } from '@angular/cdk/clipboard';

@Component({
  selector: 'app-share',
  imports: [ModalComponent, TranslocoDirective],
  templateUrl: './share.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShareComponent {
  public dashboard!: Dashboard;
  public close$: Subject<void> = new Subject();
  public copied = signal<boolean>(false);

  private clipboard = inject(Clipboard);

  public close(): void {
    this.close$.next();
    this.close$.complete();
  }

  public copy(): void {
    this.clipboard.copy(this.dashboard.url);

    this.copied.set(true);

    setTimeout(() => {
      this.copied.set(false);
    }, 3000);
  }
}
