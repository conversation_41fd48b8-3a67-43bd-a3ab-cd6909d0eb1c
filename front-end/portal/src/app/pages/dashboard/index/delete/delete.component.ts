import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { DashboardService } from '@api/dashboard/services/dashboard.service';
import {
  genericToastError,
  genericToastSuccess,
} from '@app/helpers/generic-toasts';
import {
  TranslocoDirective,
  TranslocoPipe,
  TranslocoService,
} from '@jsverse/transloco';
import { HotToastService } from '@ngxpert/hot-toast';
import { catchError, Subject, tap } from 'rxjs';
import { ModalComponent } from '../../../../components/modal/modal.component';

@Component({
  selector: 'app-delete',
  imports: [TranslocoDirective, TranslocoPipe, ModalComponent],
  templateUrl: './delete.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteComponent {
  public dashboard!: Dashboard;
  public loading = signal<boolean>(false);

  public close$: Subject<boolean> = new Subject();

  private dashboardService = inject(DashboardService);
  private translocoService = inject(TranslocoService);
  private toastService = inject(HotToastService);
  private destroyRef = inject(DestroyRef);

  public close(value: boolean = false): void {
    this.close$.next(value);
    this.close$.complete();
  }

  public submit(): void {
    if (!this.dashboard || this.loading()) {
      return;
    }

    this.loading.set(true);

    this.dashboardService
      .delete(this.dashboard)
      .pipe(
        tap(() => {
          this.loading.set(false);
          this.close(true);
          genericToastSuccess(this.toastService, this.translocoService);
        }),
        catchError((err) => {
          this.loading.set(false);
          genericToastError(this.toastService, this.translocoService);
          return err;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
