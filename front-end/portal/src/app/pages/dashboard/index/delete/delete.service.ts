import { inject, Injectable } from '@angular/core';
import { Dashboard } from '@api/dashboard/models/dashboard.interface';
import { ModalService } from '@app/services/modal.service';
import { DeleteComponent } from './delete.component';
import { take, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DeleteService {
  private modalService = inject(ModalService);

  public show(dashboard: Dashboard): Promise<boolean> {
    const modal = this.modalService.attach(DeleteComponent);

    modal.componentRef.instance.dashboard = dashboard;

    return new Promise<boolean>((resolve) => {
      modal.componentRef.instance.close$
        .pipe(
          tap((value) => {
            resolve(value);
            modal.overlayRef.detach();
          }),
          take(1),
        )
        .subscribe();
    });
  }
}
