@if (form) {
  <div *transloco="let t; read: 'pages.auth.register'">
    <h2 class="text-3xl font-bold text-center text-eaglo-gray mb-8">{{ t('title') }}</h2>
    <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-6">
      <div class="form-group">
        <label class="block text-sm font-medium text-eaglo-gray mb-1">{{ t('name') }}</label>
        <input [formControl]="form.controls.name" type="text">
      </div>
      <div class="form-group">
        <label class="block text-sm font-medium text-eaglo-gray mb-1">{{ t('firstname') }}</label>
        <input [formControl]="form.controls.firstname" type="text">
      </div>
      <div class="form-group">
        <label class="block text-sm font-medium text-eaglo-gray mb-1">{{ t('lastname') }}</label>
        <input [formControl]="form.controls.lastname" type="text">
      </div>
      <div class="form-group">
        <label class="block text-sm font-medium text-eaglo-gray mb-1">{{ t('email') }}</label>
        <input [formControl]="form.controls.email" type="email" autocomplete="email">
      </div>
      <div class="form-group">
        <label class="block text-sm font-medium text-eaglo-gray mb-1">{{ t('password') }}</label>
        <input [formControl]="form.controls.password" type="password" autocomplete="new-password">
      </div>
      <button [class.--loading]="loading" type="submit" class="btn w-full">
        {{ 'general.buttons.register' | transloco }}
      </button>
      <div class="text-center mt-4">
        <p class="text-sm text-eaglo-gray">{{ t('already_account') }}
          <a [routerLink]="['/auth/login']" class="text-eaglo-blue hover:underline font-medium">
            {{ t('login') }}
          </a>
        </p>
      </div>
    </form>
  </div>
} 