import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { IndexRequest } from '@api/support/requests/index.request';
import { DataResponse } from '@api/support/responses/data.response';
import { Invoice } from '@api/billing/models/invoice.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@helpers/transform-params';

@Injectable({
  providedIn: 'root',
})
export class InvoiceService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: IndexRequest,
  ): Observable<DataResponse<Invoice[]> | PaginatedResponse<Invoice>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<Invoice[]> | PaginatedResponse<Invoice>
    >(`${this.endpoint}/api/v1/billing/invoices`, { params });
  }

  public download(invoice: Invoice): Observable<Blob> {
    return this.httpClient.get(
      `${this.endpoint}/api/v1/billing/invoices/${invoice.id}/download`,
      { responseType: 'blob' },
    );
  }
}
