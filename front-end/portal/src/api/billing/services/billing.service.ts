import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { SetupIntentResponse } from '@api/billing/responses/setup-intent.response';
import { Product } from '@api/billing/models/product.interface';
import { BillingSubscribeRequest } from '@api/billing/requests/billing-subscribe.request';
import { Subscription } from '@api/billing/models/subscription.interface';
import { StripeInformation } from '@api/billing/models/stripe-information.interface';
import { BillingUpdateRequest } from '@api/billing/requests/billing-update.request';

@Injectable({
  providedIn: 'root',
})
export class BillingService {
  private readonly endpoint: string | undefined;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public update(
    body: BillingUpdateRequest,
  ): Observable<DataResponse<StripeInformation>> {
    return this.httpClient.put<DataResponse<StripeInformation>>(
      `${this.endpoint}/api/billing`,
      body,
    );
  }

  public setupIntent(): Observable<DataResponse<SetupIntentResponse>> {
    return this.httpClient.get<DataResponse<SetupIntentResponse>>(
      `${this.endpoint}/api/billing/setup-intent`,
    );
  }

  public products(): Observable<DataResponse<Product[]>> {
    return this.httpClient.get<DataResponse<Product[]>>(
      `${this.endpoint}/api/billing/products`,
    );
  }

  public subscribe(
    body: BillingSubscribeRequest,
  ): Observable<DataResponse<Subscription>> {
    return this.httpClient.post<DataResponse<Subscription>>(
      `${this.endpoint}/api/billing/subscribe`,
      body,
    );
  }

  public extendTrial(): Observable<DataResponse<Subscription>> {
    return this.httpClient.post<DataResponse<Subscription>>(
      `${this.endpoint}/api/billing/extend-trial`,
      {},
    );
  }
}
