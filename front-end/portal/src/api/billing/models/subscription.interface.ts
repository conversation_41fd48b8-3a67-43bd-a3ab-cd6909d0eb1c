import { SubscriptionStatus } from '@api/billing/enums/subscription-status.enum';
import { Price } from '@api/billing/models/price.interface';
import { Product } from './product.interface';
import { SubscriptionSchedule } from '@api/billing/models/subscription-schedule.interface';

export interface Subscription {
  id: string;
  product_id: string;
  status: SubscriptionStatus;
  valid: boolean;
  trail_ends_at: string | null;
  period_starts_at: string | null;
  trial_days_remaining: number | null;
  price?: Price;
  product?: Product;
  active_schedule?: SubscriptionSchedule;
}
