import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IndexRequest } from '@api/support/requests/index.request';
import { DataResponse } from '@api/support/responses/data.response';
import { ConfigService } from '@app/services/config.service';
import { Observable } from 'rxjs';
import { User } from '../models/user.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@app/helpers/transform-params';
import { UserStoreRequest } from '../requests/user-store.request';
import { UserRequest } from '../requests/user.request';
import { bodyToFormData } from '@app/helpers/body-to-form-data';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: IndexRequest,
  ): Observable<DataResponse<User[]> | PaginatedResponse<User>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<DataResponse<User[]> | PaginatedResponse<User>>(
      `${this.endpoint}/api/v1/users`,
      { params },
    );
  }

  public show(id: number): Observable<DataResponse<User>> {
    return this.httpClient.get<DataResponse<User>>(
      `${this.endpoint}/api/v1/users/${id}`,
    );
  }

  public store(body: UserStoreRequest): Observable<DataResponse<User>> {
    return this.httpClient.post<DataResponse<User>>(
      `${this.endpoint}/api/v1/users`,
      bodyToFormData(body),
    );
  }

  public update(user: User, body: UserRequest): Observable<DataResponse<User>> {
    return this.httpClient.post<DataResponse<User>>(
      `${this.endpoint}/api/v1/users/${user.id}`,
      bodyToFormData(body),
    );
  }

  public delete(user: User): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/v1/users/${user.id}`,
    );
  }
}
