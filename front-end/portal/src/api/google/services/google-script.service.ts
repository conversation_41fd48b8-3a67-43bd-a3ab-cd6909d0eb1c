import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { GoogleScriptStoreRequest } from '@api/google/requests/google-script-store.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { GoogleScript } from '@api/google/models/google-script.interface';

@Injectable({
  providedIn: 'root',
})
export class GoogleScriptService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public store(
    body: GoogleScriptStoreRequest,
  ): Observable<DataResponse<GoogleScript>> {
    return this.httpClient.post<DataResponse<GoogleScript>>(
      `${this.endpoint}/api/v1/google/scripts`,
      body,
    );
  }

  public show(script: GoogleScript): Observable<DataResponse<GoogleScript>> {
    return this.httpClient.get<DataResponse<GoogleScript>>(
      `${this.endpoint}/api/v1/google/scripts/${script.id}`,
    );
  }

  public script(script: GoogleScript): Observable<string> {
    return this.httpClient.get<string>(
      `${this.endpoint}/api/v1/google/scripts/${script.id}/script`,
    );
  }
}
