import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { IndexRequest } from '@api/support/requests/index.request';
import { Observable } from 'rxjs';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { GoogleDrive } from '@api/google/models/google-drive.interface';
import { DataResponse } from '@api/support/responses/data.response';
import { paramsToHttpParams } from '@helpers/transform-params';

@Injectable({
  providedIn: 'root',
})
export class GoogleDriveService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: IndexRequest,
  ): Observable<PaginatedResponse<GoogleDrive> | DataResponse<GoogleDrive[]>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      PaginatedResponse<GoogleDrive> | DataResponse<GoogleDrive[]>
    >(`${this.endpoint}/api/v1/google/drive`, { params });
  }
}
