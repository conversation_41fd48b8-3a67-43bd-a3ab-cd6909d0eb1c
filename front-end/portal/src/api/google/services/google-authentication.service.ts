import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DataResponse } from '@api/support/responses/data.response';
import { ConfigService } from '@app/services/config.service';
import { Observable } from 'rxjs';
import { GoogleAuthenticationOauthRedirectLinkResponse } from '../responses/google-authentication-oauth-redirect-link.response';

@Injectable({
  providedIn: 'root',
})
export class GoogleAuthenticationService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public oauthRedirectLink(): Observable<
    DataResponse<GoogleAuthenticationOauthRedirectLinkResponse>
  > {
    return this.httpClient.post<
      DataResponse<GoogleAuthenticationOauthRedirectLinkResponse>
    >(`${this.endpoint}/api/v1/google/authentication/oauthRedirectLink`, {});
  }
}
