import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IndexRequest } from '@api/support/requests/index.request';
import { DataResponse } from '@api/support/responses/data.response';
import { ConfigService } from '@app/services/config.service';
import { Observable } from 'rxjs';
import { GoogleAdAccount } from '../models/google-ad-account.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@app/helpers/transform-params';
import { GoogleAdAccountIndexRequest } from '@api/google/requests/google-ad-account-index.request';

@Injectable({
  providedIn: 'root',
})
export class GoogleAdAccountService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: GoogleAdAccountIndexRequest,
  ): Observable<
    DataResponse<GoogleAdAccount[]> | PaginatedResponse<GoogleAdAccount>
  > {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<GoogleAdAccount[]> | PaginatedResponse<GoogleAdAccount>
    >(`${this.endpoint}/api/v1/google/ad/accounts`, { params });
  }

  public show(id: number): Observable<DataResponse<GoogleAdAccount>> {
    return this.httpClient.get<DataResponse<GoogleAdAccount>>(
      `${this.endpoint}/api/v1/google/ad/accounts/${id}`,
    );
  }
}
