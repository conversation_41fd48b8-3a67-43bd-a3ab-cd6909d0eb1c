import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IndexRequest } from '@api/support/requests/index.request';
import { DataResponse } from '@api/support/responses/data.response';
import { ConfigService } from '@app/services/config.service';
import { Observable } from 'rxjs';
import { Dashboard } from '../models/dashboard.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@app/helpers/transform-params';
import { DashboardRequest } from '../requests/dashboard.request';
import { DashboardConfigurationResponse } from '../responses/dashboard-configuration.response';

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: IndexRequest,
  ): Observable<DataResponse<Dashboard[]> | PaginatedResponse<Dashboard>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<Dashboard[]> | PaginatedResponse<Dashboard>
    >(`${this.endpoint}/api/v1/dashboards`, { params });
  }

  public show(id: number): Observable<DataResponse<Dashboard>> {
    return this.httpClient.get<DataResponse<Dashboard>>(
      `${this.endpoint}/api/v1/dashboards/${id}`,
    );
  }

  public store(body: DashboardRequest): Observable<DataResponse<Dashboard>> {
    return this.httpClient.post<DataResponse<Dashboard>>(
      `${this.endpoint}/api/v1/dashboards`,
      body,
    );
  }

  public update(
    dashboard: Dashboard,
    body: DashboardRequest,
  ): Observable<DataResponse<Dashboard>> {
    return this.httpClient.put<DataResponse<Dashboard>>(
      `${this.endpoint}/api/v1/dashboards/${dashboard.id}`,
      body,
    );
  }

  public delete(dashboard: Dashboard): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/v1/dashboards/${dashboard.id}`,
    );
  }

  public configuration(): Observable<
    DataResponse<DashboardConfigurationResponse>
  > {
    return this.httpClient.get<DataResponse<DashboardConfigurationResponse>>(
      `${this.endpoint}/api/v1/dashboards/configuration`,
    );
  }
}
