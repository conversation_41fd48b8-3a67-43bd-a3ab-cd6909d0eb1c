import { User } from '@api/profile/models/user.interface';
import { Page } from './page.interface';
import { DashboardSource } from './dashboard-source.interface';
import { Recipient } from './recipient.interface';
import { DashboardBusinessType } from '@api/dashboard/enums/dashboard-business-type.enum';

export interface Dashboard {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
  url: string;
  business_type: DashboardBusinessType | null;
  pages?: Page[];
  user?: User;
  sources?: DashboardSource[];
  recipient?: Recipient;
}
