import { WidgetType } from '../enums/widget-type.enum';
import { DashboardBusinessType } from '@api/dashboard/enums/dashboard-business-type.enum';

export interface DashboardRequest {
  general: DashboardGeneralRequest;
  pages: DashboardPageRequest[];
  sources: DashboardSourcesRequest;
  recipient: DashboardRecipientRequest;
  kpis: DashboardKpiRequest[];
}

export interface DashboardGeneralRequest {
  name: string;
  user_id: number;
  business_type: DashboardBusinessType;
}

export interface DashboardPageRequest {
  type: string;
  sections: DashboardSectionRequest[];
}

export interface DashboardSectionRequest {
  type: string;
}

export interface DashboardSourcesRequest {
  google_ad_account_ids: number[] | null;
}

export interface DashboardRecipientRequest {
  company: string;
}

export interface DashboardKpiRequest {
  type: WidgetType;
  entries: {
    year: number;
    target: number;
    unit: string | null;
  }[];
}
