import { Injectable } from '@angular/core';
import { ConfigService } from '@services/config.service';
import { HttpClient } from '@angular/common/http';
import { IndexRequest } from '@api/support/requests/index.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { Email } from '@api/email/models/email.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import { paramsToHttpParams } from '@helpers/transform-params';
import { EmailRequest } from '@api/email/requests/email.request';
import { EmailAddLanguageRequest } from '@api/email/requests/email-add-language.request';
import { EmailIndexRequest } from '@api/email/requests/email-index.request';

@Injectable({
  providedIn: 'root',
})
export class EmailService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: EmailIndexRequest,
  ): Observable<DataResponse<Email[]> | PaginatedResponse<Email>> {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<Email[]> | PaginatedResponse<Email>
    >(`${this.endpoint}/api/v1/emails`, { params });
  }

  public show(id: string): Observable<DataResponse<Email>> {
    return this.httpClient.get<DataResponse<Email>>(
      `${this.endpoint}/api/v1/emails/${id}`,
    );
  }

  public update(
    email: Email,
    body: EmailRequest,
  ): Observable<DataResponse<Email>> {
    return this.httpClient.put<DataResponse<Email>>(
      `${this.endpoint}/api/v1/emails/${email.id}`,
      body,
    );
  }

  public addLanguage(
    email: Email,
    body: EmailAddLanguageRequest,
  ): Observable<DataResponse<Email>> {
    return this.httpClient.post<DataResponse<Email>>(
      `${this.endpoint}/api/v1/emails/${email.id}/add-language`,
      body,
    );
  }

  public delete(email: Email): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/v1/emails/${email.id}`,
    );
  }

  public preview(email: Email): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/emails/${email.id}/preview`,
      {},
    );
  }
}
