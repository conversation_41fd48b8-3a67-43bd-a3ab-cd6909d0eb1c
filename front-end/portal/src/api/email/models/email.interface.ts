import { User } from '@api/profile/models/user.interface';

export interface Email {
  id: string;
  account_id: string | null;
  name: string;
  updated_at: string;
  updated_by?: User;
  variables: {
    [key: string]: {
      name: string;
      value: string;
    };
  };
  language: string;
  cc_sender: boolean;
  cc: string[] | null;
  subject: string | null;
  html: string | null;
  event: string;
}
