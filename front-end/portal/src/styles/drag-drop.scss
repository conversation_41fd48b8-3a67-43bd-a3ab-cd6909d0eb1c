.drag-drop-container {
  min-height: 60px;
  display: block;
  overflow: hidden;
}
.cdk-drag-preview {
  @apply border-0 rounded drop-shadow-lg;
}
.cdk-drag-placeholder {
  opacity: 0;
}
.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
.drag-drop-item:last-child {
  border: none;
}
.drag-drop-container.cdk-drop-list-dragging .drag-drop-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
