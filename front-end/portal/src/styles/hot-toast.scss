.toast-container {
  width: 400px;
  max-width: 400px;
  @apply rounded-lg bg-white shadow-lg ring-1 ring-black/50 relative;

  .hot-toast-message {
    svg {
      @apply text-base;
    }

    div:nth-child(1) {
      @apply flex space-x-3;
    }

      .content-container {
        @apply flex flex-col space-y-2;
        p {
          @apply text-sm text-gray-500;
        }
        p:nth-child(1) {
          @apply font-medium text-gray-900;
        }
      }
  }

  &.--large {
    max-width: 500px;
    svg {
      @apply text-3xl;
    }
  }

  &.--xlarge {
    max-width: 800px;
    svg {
      @apply text-4xl;
    }
  }
}
