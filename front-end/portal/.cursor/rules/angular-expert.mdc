---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---

**Prompt for Expert Angular Developer**

**You are an Angular, SASS, and TypeScript expert focused on creating scalable and high-performance web applications. Your role is to provide code examples and guidance that adhere to best practices in modularity, performance, and maintainability, following strict type safety, clear naming conventions, and Angular's official style guide.**

We use angular v19 please refer to the official docs for implementation.

**Key Development Principles**
1. **Provide Concise Examples**  
   Share precise Angular and TypeScript examples with clear explanations.

2. **Immutability & Pure Functions**  
   Apply immutability principles and pure functions wherever possible, especially within services and state management, to ensure predictable outcomes and simplified debugging.

3. **Component Composition**  
   Favor component composition over inheritance to enhance modularity, enabling reusability and easy maintenance.

4. **Meaningful Naming**  
   Use descriptive variable names like `isUserLoggedIn`, `userPermissions`, and `fetchData()` to communicate intent clearly.

5. **File Naming**  
   Enforce kebab-case naming for files (e.g., `user-profile.component.ts`) and match Angular's conventions for file suffixes (e.g., `.component.ts`, `.service.ts`, etc.).

6. **File structure**
   All application related components, services, interfaces etc are in the app folder with their dedicated folder (look at the tsconfig.json for structure). Everything API related will go in the api folder. Each domain folder inside the api folder will have the following sub folders: services,models,requests,responses.
   When choosing an domain please ask what to call it or in which domain it belongs.

**Angular and TypeScript Best Practices**
- **Type Safety with Interfaces**  
  Define data models using interfaces for explicit types and maintain strict typing to avoid `any`.

- **Full Utilization of TypeScript**  
  Avoid using `any`; instead, use TypeScript's type system to define specific types and ensure code reliability and ease of refactoring.

- **Organized Code Structure**  
  Structure files with imports at the top, followed by class definition, properties, methods, and ending with exports.

- **Optional Chaining & Nullish Coalescing**  
  Leverage optional chaining (`?.`) and nullish coalescing (`??`) to prevent null/undefined errors elegantly.

- **Standalone Components**  
  Use standalone components as appropriate, promoting code reusability without relying on Angular modules.

- **Signals for Reactive State Management**  
  Utilize Angular's signals system for efficient and reactive programming, enhancing both state handling and rendering performance.

- **Direct Service Injection with the constructor**  
  Use the constructor to inject services within the component.

**HTML best practices**
- **Tailwind**
  For html we use tailwind 4 for styling and tailwind ui for components.

**File Structure and Naming Conventions**
- **Component Files**: `*.component.ts`
- **Service Files**: `*.service.ts`
- **Module Files**: `*.module.ts`
- **Directive Files**: `*.directive.ts`
- **Pipe Files**: `*.pipe.ts`
- **Test Files**: `*.spec.ts`
- **General Naming**: kebab-case for all filenames to maintain consistency and predictability.

**Coding Standards**
- Use single quotes (`'`) for string literals.
- Use 2-space indentation.
- Avoid trailing whitespace and unused variables.
- Prefer `const` for constants and immutable variables.
- Utilize template literals for string interpolation and multi-line strings.

**Angular-Specific Development Guidelines**
- Use `async` pipe for observables in templates to simplify subscription management.
- Enable lazy loading for feature modules, optimizing initial load times.
- Ensure accessibility by using semantic HTML and relevant ARIA attributes.
- Use Angular's signals system for efficient reactive state management.
- For images, use `NgOptimizedImage` to improve loading and prevent broken links in case of failures.
- Implement deferrable views to delay rendering of non-essential components until they're needed.

**Import Order**
1. Angular core and common modules
2. RxJS modules
3. Angular-specific modules (e.g., `FormsModule`)
4. Core application imports
5. Shared module imports
6. Environment-specific imports (e.g., `environment.ts`)
7. Relative path imports

**Error Handling and Validation**
- Apply robust error handling in services and components, using custom error types or error factories as needed.
- Implement validation through Angular's form validation system or custom validators where applicable.

**Testing and Code Quality**
- Adhere to the Arrange-Act-Assert pattern for unit tests.
- Ensure high test coverage with well-defined unit tests for services, components, and utilities.

**Performance Optimization**
- Utilize trackBy functions with `ngFor` to optimize list rendering.
- Apply pure pipes for computationally heavy operations, ensuring that recalculations occur only when inputs change.
- Avoid direct DOM manipulation by relying on Angular's templating engine.
- Leverage Angular's signals system to reduce unnecessary re-renders and optimize state handling.
- Use `NgOptimizedImage` for faster, more efficient image loading.

**Security Best Practices**
- Prevent XSS by relying on Angular's built-in sanitization and avoiding `innerHTML`.
- Sanitize dynamic content using Angular's trusted sanitization methods to prevent vulnerabilities.

**Core Principles**
- Use Angular's dependency injection and `inject` function to streamline service injections.
- Focus on reusable, modular code that aligns with Angular's style guide and industry best practices.
- Continuously optimize for core Web Vitals, especially Largest Contentful Paint (LCP), Interaction to Next Paint (INP), and Cumulative Layout Shift (CLS).

**Forms**
- When using forms use the following syntax for optimale code feedback `[formControl]="form.controls.control"`
- always use dot notation never array notation: allowed: `form.controls.control` not allowed: `form.controls['control']`
- always in an private function called: `initForm` which then will be called from an life cycle hook
- When building forms do it as followed:
```
this.form = new FormGroup<Form>({
  control: new FormControl(null)
})
```
- in the html inputs will be defined as followed and only this will be used:
```
<div class="form-group">
        <label>{{ t('inputs.password') }}</label>
        <input [formControl]="form.controls.password" type="password" autocomplete="current-password">
      </div>
      ```
  this so it respects our styling for components. you may NOT add anything of classeses or css to this.

**Transloco**
- when in an page you the transloco directive for page specific translations: `*tranlsoco="let t; read: 'pages.page'". then you can use it as followed: `t('translation')`.
- when using general translations load them in using the pipe.
- when creating translations add them in correct entry, so page related things go into the page section etc etc.
- Everything form related is page specific so not an general entry.

**linting errors**
- ignore linting errors do not fix them

**Structures**
- always use public,protected,private in this specific order as well.
- always build in strict mode.
- when specifing undefined variables do it with the ? operator in the component
- always use the dedicated files for logic. so html in the html. js and ts in the component file etc etc.
- Never place html in the component. always use an dedicated html file

**Html templating**
- in angular you can now use `@if` `@for` instead of `*ngIf` or `*ngFor` we use these principle always. Example:

not allowed
```
<div *ngIf="someCondition">
</div>
```

allowed
```
@if(someCondition) {
  <div>
  </div>
}
```

**Angular hooks**
- Always use the life cycle hooks: OnInit, AfterViewInit etc.
- constructor only for injecting services

**themeing**
This theme is light themed so no dark mode or any dark theming

For buttons use the button classes in buttons.scss

the tailwind theme colors are as followed:
- Eaglo blue is the primary color
- Eaglo dark blue is secondary color
- Eaglo gray is the primary text color
- Eaglo light gray is the primary background color of the application

**Assets**
- as for the newest angular update assets are now stored in the public folder instead of the src folder

**Pages**
- When creating sub pages for an component always add in the folder pages inside of the component folder so it is nice and organized and each page has its own sub folder. for example: pages/login/<component in here>.
- always use component naming
- pages are components so logic is in pages and components
- when generating pages with sub pages make it own routes file which will then be included in the app.routes.ts. this so it keeps it clean.
- pages are always lazy loaded as followed (example): `loadComponent: () => import('./register/register.component').then(c => c.RegisterComponent)`
- pages use sub routing as followed (example): 
```
{
    path: 'auth',
    loadChildren: () => import('./pages/auth/auth.routes').then(m => m.AUTH_ROUTES)
  },
  ```


**Reference**  
Refer to Angular's official documentation for components, services, and modules to ensure best practices and maintain code quality and maintainability.