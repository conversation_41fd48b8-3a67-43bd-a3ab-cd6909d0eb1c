/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "alwaysStrict": true,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "useDefineForClassFields": false,
    "allowSyntheticDefaultImports": true,
    "lib": [
      "ES2022",
      "dom"
    ],
    "paths": {
      "@environments/*": ["src/environments/*"],
      "@app/*": ["src/app/*"],
      "@layout/*": ["src/app/layout/*"],
      "@pages/*": ["src/app/pages/*"],
      "@interfaces/*": ["src/app/interfaces/*"],
      "@api/*": ["src/api/*"],
      "@models/*": ["src/api/interfaces/models/*"],
      "@requests/*": ["src/api/interfaces/requests/*"],
      "@responses/*": ["src/api/interfaces/responses/*"],
      "@services/*": ["src/app/services/*"],
      "@guards/*": ["src/app/guards/*"],
      "@interceptors/*": ["src/app/interceptors/*"],
      "@directives/*": ["src/app/directives/*"],
      "@pipes/*": ["src/app/pipes/*"],
      "@helpers/*": ["src/app/helpers/*"],
      "@modules/*": ["src/app/modules/*"],
      "@components/*": ["src/app/components/*"],
      "@enums/*": ["src/app/interfaces/enums/*"],
      "@utils/*": ["src/app/utils/*"],
      "@validators/*": ["src/app/validators/*"],
      "@packages/*": ["../packages/*"],
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
