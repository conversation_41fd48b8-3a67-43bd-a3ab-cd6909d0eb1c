const winston = require('winston');

function logProvider() {
  return winston.createLogger({
    level: 'debug',
    format: winston.format.combine(
      winston.format.splat(),
      winston.format.simple()
    ),
    transports: [new winston.transports.Console()],
  });
}

var PROXY_CONF = {
  "/api": {
    target: "http://api.eaglo.test/",
    "pathRewrite": {
      "^/api": ""
    },
    secure: false,
    logLevel: "info", // debug | info | warning | error
    logProvider: logProvider,
    changeOrigin: true
  }
};

module.exports = PROXY_CONF;
